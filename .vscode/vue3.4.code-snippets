{"Vue3.4+defineOptions+BaseTableSearchContainer": {"scope": "vue", "prefix": "Vue3.4+", "body": ["<template>", "  <div class=\"app-container\">", "    <BaseTableSearchContainer @size-changed=\"tableResize\">", "      <template #search>", "        <TBSearchContainer :is-show-toggle=\"true\">", "          <template #left>", "            <el-form :model=\"queryParams\" label-position=\"right\" :inline=\"true\">", "              <el-form-item label=\"时间\">", "                <el-date-picker", "                  v-model=\"timeRange\"", "                  unlink-panels", "                  type=\"daterange\"", "                  range-separator=\"至\"", "                  start-placeholder=\"开始日期\"", "                  end-placeholder=\"结束日期\"", "                  format=\"YYYY-MM-DD\"", "                  :clearable=\"false\"", "                  value-format=\"YYYY-MM-DD\"", "                  style=\"width: 250px\"", "                />", "              </el-form-item>", "              <el-form-item label=\"机构\">", "                <HospitalSelect v-model=\"queryParams.sourceOrganizationNameKeyword\" keyId=\"Name\" />", "              </el-form-item>", "              <el-form-item label=\"是否测试数据\">", "                <el-select", "                  v-model=\"queryParams.isTest\"", "                  placeholder=\"请选择\"", "                  clearable", "                  :empty-values=\"[null, undefined, '']\"", "                  :value-on-clear=\"() => null\"", "                >", "                  <el-option label=\"是\" value=\"是\" />", "                  <el-option label=\"否\" value=\"否\" />", "                </el-select>", "              </el-form-item>", "              <el-form-item label=\"关键字\" prop=\"keyword\">", "                <el-input v-model=\"queryParams.keyword\" placeholder=\"姓名/电话号码\" @keyup.enter=\"handleQuery\" />", "              </el-form-item>", "            </el-form>", "          </template>", "          <template #right>", "            <el-button type=\"primary\" icon=\"search\" @click=\"handleQuery\">搜索</el-button>", "          </template>", "        </TBSearchContainer>", "      </template>", "      <template #table>", "        <el-table", "          ref=\"tableRef\"", "          v-loading=\"tableLoading\"", "          :data=\"pageData\"", "          :total=\"total\"", "          border", "          row-key=\"Id\"", "          :height=\"tableFluidHeight\"", "          highlight-current-row", "          style=\"text-align: center; flex: 1\"", "        >", "          <el-table-column prop=\"Name\" label=\"姓名\" align=\"center\" />", "          <el-table-column prop=\"Sex\" label=\"性别\" align=\"center\" />", "          <el-table-column prop=\"Age\" label=\"年龄\" align=\"center\" />", "          <el-table-column prop=\"HasVisitedRegister\" label=\"是否报道\" align=\"center\" />", "          <el-table-column prop=\"ConsultFinishCount\" label=\"问诊次数\" align=\"center\" />", "          <el-table-column prop=\"TreatFinishCount\" label=\"咨询次数\" align=\"center\" />", "          <el-table-column prop=\"PhoneNumber\" label=\"注册电话\" align=\"center\" width=\"180\" />", "", "          <el-table-column label=\"医生/治疗师\" prop=\"inviterUser\" align=\"center\">", "            <template #default=\"scope\">", "              {{ scope.row.inviterUser }}", "            </template>", "          </el-table-column>", "          <el-table-column fixed=\"right\" label=\"操作\" width=\"150\" align=\"center\">", "            <template #default=\"scope\">", "              <el-button link type=\"primary\" @click=\"handlePreviewOrEdit(scope.row, true)\">", "                查看", "              </el-button>", "              <el-button", "                v-hasNoPermission=\"['promoter']\"", "                link", "                type=\"primary\"", "                @click=\"handlePreviewOrEdit(scope.row, false)\"", "              >", "                编辑", "              </el-button>", "            </template>", "          </el-table-column>", "        </el-table>", "      </template>", "      <template #pagination>", "        <Pagination", "          v-if=\"total > 0\"", "          v-model:total=\"total\"", "          v-model:page=\"queryParams.PageIndex\"", "          v-model:limit=\"queryParams.PageSize\"", "          @pagination=\"handleGetTableList\"", "        />", "      </template>", "    </BaseTableSearchContainer>", "  </div>", "</template>", "", "<script setup lang=\"ts\">", "import dayjs from \"dayjs\";", "import { useTableConfig } from \"@/hooks/useTableConfig\";", "", "defineOptions({", "  name: \"\",", "});", "", "const queryParams = ref<any>({});", "const timeRange = ref<[string, string]>([", "  dayjs().format(\"YYYY-MM-01\"),", "  dayjs().format(\"YYYY-MM-DD\"),", "]);", "", "const isPreview = ref<boolean>(false);", "provide(\"isPreview\", isPreview);", "", "const { tableLoading, pageData, total, tableRef, exportLoading, tableDateFormat, tableFluidHeight, tableResize } =", "  useTableConfig<unknown>();", "", "const handleQuery = () => {", "  queryParams.value.PageIndex = 1;", "  handleGetTableList();", "};", "", "const handlePreviewOrEdit = async (row: BaseOrganization | null, isPreviewState: boolean) => {", "  isPreview.value = row ? isPreviewState : false;", "};", "const handleGetTableList = () => {};", "", "watch(timeRange, (newVal) => {", "  queryParams.value.QueryStartDate = dayjs(newVal[0]).format(\"YYYY-MM-DD 00:00:00\");", "  queryParams.value.QueryEndDate = dayjs(newVal[1]).format(\"YYYY-MM-DD 23:59:59\");", "});", "", "onActivated(() => {", "  handleGetTableList();", "});", "", "", "</script>", "", "<style lang=\"scss\" scoped></style>", ""], "description": "Vue3.4+defineOptions+BaseTableSearchContainer"}, "Vue3.4+SelectionalTable": {"scope": "vue", "prefix": "Vue3.4", "body": ["<template>", "  <div class=\"app-container\">", "    <BaseTableSearchContainer @size-changed=\"tableResize\">", "      <!-- 顶部筛选条件 -->", "      <template #search>", "        <TBSearchContainer :is-show-toggle=\"true\">", "          <template #left>", "            <el-form :model=\"queryParams\" label-position=\"right\" :inline=\"true\">", "              <!-- 选择时间范围 -->", "              <el-form-item label=\"时间\">", "                <el-date-picker", "                  v-model=\"dateRange\"", "                  type=\"daterange\"", "                  range-separator=\"至\"", "                  start-placeholder=\"开始日期\"", "                  end-placeholder=\"结束日期\"", "                  :clearable=\"false\"", "                  value-format=\"YYYY-MM-DD HH:mm:ss\"", "                  unlink-panels", "                  class=\"w-300px!\"", "                />", "              </el-form-item>", "              <!-- 选择机构 -->", "              <el-form-item label=\"医院\">", "                <HospitalSelect v-model=\"queryParams.OrgId\" @change=\"() => { queryParams.DeptId = undefined; queryParams.UserId = undefined; }\" />", "              </el-form-item>", "              <!-- 选择科室 -->", "              <el-form-item label=\"科室\">", "                <DeptSelect", "                  v-model=\"queryParams.DeptId\"", "                  :org-id=\"queryParams.OrgId\"", "                  :disabled=\"!queryParams.OrgId\"", "                />", "              </el-form-item>", "              <!-- 选择用户 -->", "              <el-form-item label=\"用户\">", "                <UserSelect", "                  v-model=\"queryParams.UserId\"", "                  :disabled=\"!queryParams.OrgId\"", "                  :org-ids=\"queryParams.OrgId ? [queryParams.OrgId!] : null\"", "                  :dept-ids=\"queryParams.DeptId ? [queryParams.DeptId!] : null\"", "                />", "              </el-form-item>", "              <!-- 网络请求下拉单选 -->", "              <el-form-item label=\"网络请求下拉单选\">", "                <KSelect", "                  v-model=\"queryParams.data\"", "                  :data=\"[]\"", "                  :props=\"{ label: 'Key（显示字段）', value: 'Id（选中后获取字段）' }\"", "                  :loading=\"dataLoading\"", "                  :show-all=\"true\"", "                />", "              </el-form-item>", "              <!-- 静态数据下拉单选 -->", "              <el-form-item label=\"静态数据下拉单选\">", "                <KSelect", "                  v-model=\"queryParams.IsEnble\"", "                  :data=\"[", "                    { label: '是', value: true },", "                    { label: '否', value: false },", "                  ]\"", "                  :show-all=\"true\"", "                />", "              </el-form-item>", "              <el-form-item label=\"关键字\">", "                <el-input", "                  v-model=\"queryParams.keyword\"", "                  placeholder=\"请输入\"", "                  clearable", "                  @keyup.enter=\"handleQuery\"", "                />", "              </el-form-item>", "            </el-form>", "          </template>", "          <template #right>", "            <el-button type=\"primary\" icon=\"search\" @click=\"handleQuery\">搜索</el-button>", "            <el-button type=\"primary\" @click=\"onAddItem\">新增</el-button>", "          </template>", "        </TBSearchContainer>", "      </template>", "      <!-- 列表 -->", "      <template #table>", "        <el-table", "          :ref=\"kTableRef\"", "          v-loading=\"tableLoading\"", "          :data=\"pageData\"", "          :total=\"total\"", "          row-key=\"Id\"", "          :height=\"tableFluidHeight\"", "          :header-cell-style=\"{ textAlign: 'center' }\"", "          :cell-style=\"{ textAlign: 'center' }\"", "          border", "          highlight-current-row", "          @selection-change=\"(selection) => handleSelectionChange(selection, 'Id')\"", "        >", "          <!-- 可选项框 -->", "          <el-table-column type=\"selection\" width=\"55\" />", "          <!-- 自定义项 -->", "          <el-table-column prop=\"Key\" label=\"自定义项\">", "            <template #default=\"scope\">", "              <!-- 最多2行 -->", "              <span class=\"line-clamp-2\">{{ scope.row.Key }}</span>", "            </template>", "          </el-table-column>", "          <!-- 操作 -->", "          <el-table-column fixed=\"right\" label=\"操作\" width=\"150\">", "            <template #default=\"scope\">", "              <el-button link type=\"primary\" @click=\"onPreviewOrEdit(scope.row, true)\">", "                查看", "              </el-button>", "              <el-button", "                v-hasNoPermission=\"['promoter']\"", "                link", "                type=\"primary\"", "                @click=\"onPreviewOrEdit(scope.row, false)\"", "              >", "                编辑", "              </el-button>", "            </template>", "          </el-table-column>", "        </el-table>", "      </template>", "      <!-- 分页 -->", "      <template #pagination>", "        <Pagination", "          v-if=\"total > 0\"", "          v-model:total=\"total\"", "          v-model:page=\"queryParams.PageIndex\"", "          v-model:limit=\"queryParams.PageSize\"", "          @pagination=\"requestTableList\"", "        />", "      </template>", "    </BaseTableSearchContainer>", "  </div>", "", "  <!-- 添加/编辑/查看 -->", "  <el-dialog", "    v-model=\"showDataDialog.isShow\"", "    :title=\"showDataDialog.title\"", "    width=\"800\"", "    destroy-on-close", "    @close=\"showDataDialog.isShow = false\"", "  >", "    <ItemForm", "      :data=\"showDataDialog.data\"", "      :disabled=\"showDataDialog.disabled\"", "      @cancel=\"showDataDialog.isShow = false\"", "      @submit=\"onConfirmSubmitItem\"", "    />", "  </el-dialog>", "  <!-- 选择机构 -->", "  <el-dialog v-model=\"showOrgDialog\" title=\"选择机构\" width=\"700px\" destroy-on-close>", "    <HospitalTransfer", "      :loading=\"orgDialogLoading\"", "      @cancel=\"showOrgDialog = false\"", "      @submit=\"onConfirmOrganizations\"", "    />", "  </el-dialog>", "</template>", "", "<script setup lang=\"ts\">", "import dayjs from \"dayjs\";", "import { useTableConfig } from \"@/hooks/useTableConfig\";", "import useOrgDialog from \"@/hooks/useOrgDialog\";", "", "/** 调试开关 */", "const kEnableDebug = true;", "defineOptions({", "  name: \"\",", "});", "", "const {", "  kTableRef,", "  tableRef,", "  pageData,", "  tableLoading,", "  tableFluidHeight,", "  total,", "  tableResize,", "  handleSelectionChange,", "  selectedTableIds,", "  tableDateFormat,", "} = useTableConfig<T>();", "const { showOrgDialog, orgDialogLoading } = useOrgDialog();", "", "/** 查询条件 */", "const queryParams = reactive<S>({", "  BeginTime: dayjs().startOf(\"month\").format(\"YYYY-MM-DD HH:mm:ss\"),", "  EndTime: dayjs().endOf(\"day\").format(\"YYYY-MM-DD HH:mm:ss\"),", "  PageIndex: 1,", "  PageSize: 20,", "});", "", "/** 定义 date<PERSON><PERSON>e */", "const dateRange = computed({", "  get() {", "    /** 从 queryParams 中获取日期范围 */", "    return [queryParams.BeginTime, queryParams.EndTime];", "  },", "  set(newValue) {", "    /** 当用户选择日期范围时，更新 queryParams */", "    if (newValue && newValue.length === 2) {", "      queryParams.BeginTime = newValue[0].split(\" \")[0] + \" 00:00:00\";", "      queryParams.EndTime = newValue[1].split(\" \")[0] + \" 23:59:59\";", "    }", "  },", "});", "", "/** 查看/添加/编辑弹窗 */", "const showDataDialog = reactive({", "  isShow: false,", "  title: \"\",", "  disabled: false,", "  data: {} as T, // 查看/添加/编辑详情", "});", "", "/** 点击搜索 */", "function handleQuery() {", "  queryParams.PageIndex = 1;", "  requestTableList();", "}", "", "/** 点击添加 */", "function onAddItem() {", "  kEnableDebug && console.debug(\"点击添加\");", "", "  showDataDialog.title = \"新增xx\";", "  showDataDialog.disabled = false;", "  showDataDialog.data = {};", "  showDataDialog.isShow = true;", "}", "", "/** 点击查看/编辑 */", "async function onPreviewOrEdit(row?: T, disabled: boolean = false) {", "  kEnableDebug && console.debug(\"查看/编辑\", row, disabled);", "", "  showDataDialog.title = disabled ? \"查看xx\" : \"编辑xx\";", "  showDataDialog.disabled = disabled;", "  showDataDialog.data = row ?? {};", "  showDataDialog.isShow = true;", "}", "", "/** 确定新增提交 */", "function onConfirmSubmitItem(data: T) {", "  kEnableDebug && console.debug(\"确定提交\", data);", "", "  // 提交成功", "  showDataDialog.isShow = false;", "  ElNotification.success(\"提交成功\");", "", "  // 刷新列表", "  requestTableList();", "}", "", "/** 确定选择机构 */", "async function onConfirmOrganizations(organizationIds: string[]) {", "  kEnableDebug && console.log(\"选择机构\", organizationIds);", "", "  // 推送", "  orgDialogLoading.value = true;", "  const tableIds = selectedTableIds.value ?? [];", "  // const r = await Training_Api.pushGauge({ tableIds, organizationIds });", "  orgDialogLoading.value = false;", "  // if (r.Type !== 200) {", "  //   ElMessage.error(r.Content);", "  //   return;", "  // }", "", "  showOrgDialog.value = false;", "  ElNotification.success(\"推送成功\");", "", "  // 清空选项", "  selectedTableIds.value = [];", "  tableRef.value?.clearSelection();", "}", "", "/** 请求列表数据 */", "async function requestTableList() {", "  tableLoading.value = true;", "  // const r = await Content_Api.getTableCardOrderList(queryParams);", "  tableLoading.value = false;", "  // if (r.Type !== 200) {", "  //   ElMessage.error(r.Content);", "  //   return;", "  // }", "", "  // 请求成功", "  // ...", "}", "", "onActivated(() => {", "  requestTableList();", "});", "</script>", "", "<style lang=\"scss\" scoped></style>"], "description": "Selectional Table"}, "Vue3.4+dialog+BaseForm": {"scope": "vue", "prefix": "Vue3.4", "body": ["<template>", "  <div v-loading=\"formLoading\" class=\"p-20px overflow-y-auto h-600px\">", "    <el-form", "      :ref=\"kFormRef\"", "      :model=\"formData\"", "      :rules=\"rules\"", "      label-width=\"auto\"", "      :inline=\"true\"", "      :disabled=\"props.disabled\"", "    >", "      <el-form-item label=\"编码\" prop=\"Code\">", "        <el-input v-model=\"formData.Code\" placeholder=\"请输入编码\" />", "      </el-form-item>", "    </el-form>", "  </div>", "  <!-- 底部按钮 -->", "  <div class=\"flex justify-end\">", "    <el-button @click=\"$emit('cancel')\">取消</el-button>", "    <el-button v-if=\"!props.disabled\" type=\"primary\" @click=\"onSubmitForm()\">确定</el-button>", "  </div>", "</template>", "", "<script setup lang=\"ts\">", "import Training_Api from \"@/api/training\";", "import { FormRules, FormInstance } from \"element-plus\";", "import { useUserStore } from \"@/store\";", "", "const kEnableDebug = true;", "const kFormRef = \"ruleFormRef\";", "", "defineOptions({", "  name: \"\",", "});", "", "const props = defineProps<{", "  data: T;", "  disabled: boolean;", "}>();", "", "const emit = defineEmits<{", "  cancel: [];", "  submit: [T];", "}>();", "", "onMounted(() => {", "  const data = JSON.parse(JSON.stringify(props.data));", "  Object.assign(formData, data);", "});", "", "const formLoading = ref(false);", "/** 表单实例 */", "const formRef = useTemplateRef<FormInstance>(kFormRef);", "/** 表单数据 */", "const formData = reactive<T>({});", "", "/** 表单验证规则 */", "const rules = reactive<FormRules<T>>({", "  Code: [{ required: true, message: \"请输入编码\", trigger: \"blur\" }],", "});", "", "/** 提交表单 */", "function onSubmitForm() {", "  if (!formRef.value) return;", "", "  formRef.value.validate((valid, fields) => {", "    if (valid) {", "      requestAddOrUpdateData();", "    } else {", "      kEnableDebug && console.debug(\"提交失败\", fields);", "    }", "  });", "}", "", "/** 添加/更新数据 */", "async function requestAddOrUpdateData() {", "  kEnableDebug && console.debug(\"添加/更新数据\", formData);", "", "  formLoading.value = true;", "  const userId = useUserStore().userInfo.Id;", "  const params: T = {", "    ...form<PERSON><PERSON>,", "    CreatorId: userId,", "  };", "  const r = await Training_Api.insertOrUpdateGauge(params);", "  formLoading.value = false;", "  if (r.Type === 200) {", "    emit(\"submit\");", "  } else {", "    ElMessage.error(r.Content);", "  }", "}", "</script>", "", "<style lang=\"scss\" scoped></style>"], "description": "dialog component form"}, "Vue3.4+Table+Redash+Export": {"scope": "vue", "prefix": "Vue3.4", "body": ["<template>", "  <div class=\"app-container\">", "    <BaseTableSearchContainer @size-changed=\"tableResize\">", "      <!-- 顶部筛选条件 -->", "      <template #search>", "        <TBSearchContainer :is-show-toggle=\"true\">", "        <template #left>", "          <el-form :model=\"queryParams\" label-position=\"right\" :inline=\"true\">", "            <!-- 选择时间范围 -->", "            <el-form-item label=\"时间\">", "              <el-date-picker", "                v-model=\"dateRange\"", "                type=\"daterange\"", "                range-separator=\"至\"", "                start-placeholder=\"开始日期\"", "                end-placeholder=\"结束日期\"", "                :clearable=\"false\"", "                :disabled-date=\"handleDisabledDate\"", "                value-format=\"YYYY-MM-DD HH:mm:ss\"", "                unlink-panels", "                class=\"w-300px!\"", "              />", "            </el-form-item>", "            <!-- 选择机构 -->", "            <el-form-item label=\"医院\">", "              <HospitalSelect", "                v-model=\"queryParams.OrgId\"", "                @change=\"", "                  () => {", "                    queryParams.DeptId = undefined;", "                    queryParams.UserId = undefined;", "                  }\"", "              />", "            </el-form-item>", "            <!-- 选择科室 -->", "            <el-form-item label=\"科室\">", "              <DeptSelect", "                v-model=\"queryParams.DeptId\"", "                :org-id=\"queryParams.OrgId\"", "                :disabled=\"!queryParams.OrgId\"", "              />", "            </el-form-item>", "            <!-- 选择用户 -->", "            <el-form-item label=\"用户\">", "              <UserSelect", "                v-model=\"queryParams.UserId\"", "                :disabled=\"!queryParams.OrgId\"", "                :org-ids=\"queryParams.OrgId ? [queryParams.OrgId!] : null\"", "                :dept-ids=\"queryParams.DeptId ? [queryParams.DeptId!] : null\"", "              />", "            </el-form-item>", "            <!-- 网络请求下拉单选 -->", "            <el-form-item label=\"网络请求下拉单选\">", "              <KSelect", "                v-model=\"queryParams.data\"", "                :data=\"[]\"", "                :props=\"{ label: 'Key（显示字段）', value: 'Id（选中后获取字段）' }\"", "                :loading=\"dataLoading\"", "                :show-all=\"true\"", "              />", "            </el-form-item>", "            <!-- 静态数据下拉单选 -->", "            <el-form-item label=\"静态数据下拉单选\">", "              <KSelect", "                v-model=\"queryParams.IsEnble\"", "                :data=\"[", "                  { label: '是', value: true },", "                  { label: '否', value: false },", "                ]\"", "              />", "            </el-form-item>", "            <!-- 关键字 -->", "            <el-form-item label=\"关键字\">", "              <el-input", "                v-model=\"queryParams.keyword\"", "                prefix-icon=\"el-icon-Search\"", "                placeholder=\"请输入\"", "                clearable", "                @keyup.enter=\"handleQuery\"", "              />", "            </el-form-item>", "          </el-form>", "        </template>", "        <template #right>", "          <el-button type=\"primary\" icon=\"search\" @click=\"handleQuery\">搜索</el-button>", "          <el-button", "            type=\"primary\"", "            :disabled=\"!pageData?.length\"", "            :loading=\"exportLoading\"", "            @click=\"onExport\"", "          >", "            导出", "          </el-button>", "        </template>", "      </TBSearchContainer>", "      </template>", "      <!-- 列表 -->", "      <template #table>", "        <el-table", "          :ref=\"kTableRef\"", "          v-loading=\"tableLoading\"", "          :data=\"pageData\"", "          :total=\"total\"", "          row-key=\"Id\"", "          :height=\"tableFluidHeight\"", "          :header-cell-style=\"{ textAlign: 'center' }\"", "          :cell-style=\"{ textAlign: 'center' }\"", "          border", "          highlight-current-row", "        >", "          <!-- 自定义项 -->", "          <el-table-column prop=\"Key\" label=\"自定义项\">", "            <template #default=\"scope\">", "              <!-- 最多2行 -->", "              <span class=\"line-clamp-2\">{{ scope.row.Key }}</span>", "            </template>", "          </el-table-column>", "          <!-- 操作 -->", "          <el-table-column fixed=\"right\" label=\"操作\" width=\"150\">", "            <template #default=\"scope\">", "              <el-button link type=\"primary\" @click=\"onPreviewDetail(scope.row)\">查看</el-button>", "            </template>", "          </el-table-column>", "        </el-table>", "      </template>", "      <!-- 分页 -->", "      <template #pagination>", "        <Pagination", "          v-if=\"total > 0\"", "          v-model:total=\"total\"", "          v-model:page=\"queryParams.pageIndex\"", "          v-model:limit=\"queryParams.pageSize\"", "          @pagination=\"requestTableList\"", "        />", "      </template>", "      </BaseTableSearchContainer>", "    </div>", "</template>", "", "<script setup lang=\"ts\">", "import dayjs from \"dayjs\";", "import { useTableConfig } from \"@/hooks/useTableConfig\";", "import { convertToRedashParams, exportExcel, getExportCols } from \"@/utils/serviceUtils\";", "import Report_Api from \"@/api/report\";", "import { ExportEnum } from \"@/enums/Other\";", "import { ExportTaskRedashDTO } from \"@/api/report/types\";", "import { useUserStore } from \"@/store\";", "", "interface QueryParams extends RedashParameters<S> {", "", "}", "", "/** 调试开关 */", "const kEnableDebug = true;", "defineOptions({", "  name: \"\",", "});", "", "const {", "  kTableRef,", "  tableRef,", "  pageData,", "  tableLoading,", "  tableFluidHeight,", "  total,", "  tableResize,", "  exportLoading,", "  tableDateFormat,", "} = useTableConfig<T>();", "", "let queryResultId = -1;", "", "/** 查询条件 */", "const queryParams = reactive<QueryParams>({", "  BeginTime: dayjs().startOf(\"month\").format(\"YYYY-MM-DD HH:mm:ss\"),", "  EndTime: dayjs().endOf(\"month\").format(\"YYYY-MM-DD HH:mm:ss\"),", "  LoginUserId: useUserStore().userInfo.Id,", "  pageIndex: 1,", "  pageSize: 20,", "});", "", "/** 定义 date<PERSON><PERSON>e */", "const dateRange = computed({", "  get() {", "    /** 从 queryParams 中获取日期范围 */", "    return [queryParams.BeginTime, queryParams.EndTime];", "  },", "  set(newValue) {", "    /** 当用户选择日期范围时，更新 queryParams */", "    if (newValue && newValue.length === 2) {", "      queryParams.BeginTime = newValue[0];", "      queryParams.EndTime = newValue[1];", "    }", "  },", "});", "", "/** 禁用日期 */", "function handleDisabledDate(date: Date) {", "  return date.getTime() > Date.now();", "}", "", "/** 点击搜索 */", "function handleQuery() {", "  queryParams.pageIndex = 1;", "  requestTableList();", "}", "", "/** 点击导出 */", "async function onExport() {", "  kEnableDebug && console.debug(\"点击导出\");", "", "  const { tagKeyWordList, ...parameters } = queryParams;", "  const exportParams = convertToRedashParams(parameters, \"Report_xxxx\");", "  const params: ExportTaskRedashDTO = {", "    Cols: getExportCols(tableRef.value!.columns as any, \"@\"),", "    ExecutingParams: exportParams.parameters,", "    ExportWay: ExportEnum.PlainMySql,", "    FileName: `患者查询-${Date.now()}.xlsx`,", "    JobWaitingMs: 30000,", "    QueryResultId: queryResultId,", "    Split: \"@\",", "    MaxAge: 0,", "    PageIndex: queryParams.pageIndex,", "    PageSize: queryParams.pageSize,", "    QueryName: \"Report_xxxxxxxxxx\",", "  };", "  exportLoading.value = true;", "  try {", "    await exportExcel(params);", "  } catch (error) {", "    ElNotification.error(\"导出失败\");", "  } finally {", "    exportLoading.value = false;", "  }", "}", "", "/** 点击查看 */", "function onPreviewDetail(row: T) {", "  kEnableDebug && console.debug(\"查看\", row);", "}", "", "/** 请求列表数据 */", "async function requestTableList() {", "  tableLoading.value = true;", "  const { tagKeyWordList, ...parameters } = queryParams;", "", "  const params = convertToRedashParams(parameters, \"Report_xxxx\");", "  const r = await Report_Api.getRedashList<T>(params);", "  tableLoading.value = false;", "  if (r.Type !== 200) {", "    ElMessage.error(r.Content);", "    return;", "  }", "", "  // 请求成功", "  queryResultId = r.Data.QueryResultId;", "  pageData.value = r.Data.Data;", "  total.value = r.Data.TotalCount;", "}", "", "onActivated(() => {", "  requestTableList();", "});", "</script>", "", "<style lang=\"scss\" scoped></style>", ""], "description": "Redash Export"}}