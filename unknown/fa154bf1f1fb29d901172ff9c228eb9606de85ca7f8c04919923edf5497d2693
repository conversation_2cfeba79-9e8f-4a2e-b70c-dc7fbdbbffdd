<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="提交日期">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="机构">
                <HospitalSelect v-model="queryParams.OrgIds" multiple :scopeable="true" />
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect
                  v-model="queryParams.DeptIds"
                  :org-id="
                    queryParams.OrgIds && queryParams.OrgIds.length === 1
                      ? queryParams.OrgIds[0]
                      : null
                  "
                  :disabled="!queryParams.OrgIds || queryParams.OrgIds.length !== 1"
                  multiple
                />
              </el-form-item>
              <el-form-item label="是否测试数据">
                <el-select
                  v-model="queryParams.IsTest"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  style="width: 80px"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="订单号/电话号码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              v-hasNoPermission="['externalSeller']"
              v-loading="exportLoading"
              type="primary"
              :disabled="!pageData.length"
              @click="handleExportExcel"
            >
              导出
            </el-button>
            <el-button
              v-hasNoPermission="['externalSeller']"
              type="primary"
              @click="showRefundContent = true"
            >
              退款
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #searchTable>
        <div class="text-red p-10px">
          <span>退款金额：</span>
          <span>{{ totalAmount === null ? "暂无数据" : "¥" + totalAmount + "元" }}</span>
        </div>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="OrderNoStr" label="订单编号" align="center" />
          <el-table-column prop="CreatedTime" label="订单时间" align="center">
            <template #default="scope">
              <span>{{ dayjs(scope.row.CreatedTime).format("YYYY-MM-DD HH:mm:ss") }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="OrgName" label="医院" align="center" />
          <el-table-column prop="CreatorName" label="下达人" align="center" />
          <el-table-column prop="UserName" label="患者姓名" align="center" />
          <el-table-column prop="Title" label="退款项目" align="center" />
          <el-table-column prop="RefundPrice" label="退款金额" align="center" />
          <el-table-column prop="RefundTime" label="退款时间" align="center">
            <template #default="scope">
              <span>{{ dayjs(scope.row.RefundTime).format("YYYY-MM-DD HH:mm:ss") }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="RefundOptUserName" label="操作人" width="140" align="center" />
          <el-table-column prop="Reason" label="退款原因" align="center" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ handleGetOtherText(scope.row.Reason, "Remark") }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="Reason" label="退款标签" align="center" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ handleGetOtherText(scope.row.Reason, "Tags") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否测试数据" align="center">
            <template #default="scope">
              <span>{{ scope.row.IsTest ? "是" : "否" }}</span>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog v-model="showRefundContent" title="订单详情" width="800px" destroy-on-close>
      <RefundContent ref="refundContentRef" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showRefundContent = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handleRefundSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { AftersaleRefund, OrderAftersaleRefundInputDTO } from "@/api/order/types";
import Order_Api from "@/api/order";
import { ExportEnum } from "@/enums/Other";
import { exportExcel } from "@/utils/serviceUtils";
import Community_Api from "@/api/community";
import { useUserStore } from "@/store";
import RefundContent from "./components/RefundContent.vue";

const userStore = useUserStore();
import { useDateRangePicker } from "@/hooks/useDateRangePicker";
const { datePickerShortcuts } = useDateRangePicker();

defineOptions({
  name: "RefundManagement",
});

const queryParams = ref<OrderAftersaleRefundInputDTO>({
  OrderType: "Treatment",
  PageIndex: 1,
  PageSize: 20,
  OrgIds: null,
  RefundTimeStart: dayjs().format("YYYY-MM-01"),
  RefundTimeEnd: dayjs().format("YYYY-MM-DD"),
  IsTest: false,
  Keyword: "",
  DeptIds: null,
  LoginUserId: userStore.userInfo?.Id,
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);
const totalAmount = ref<number>(0);
const exportLoading = ref<boolean>(false);
const showRefundContent = ref<boolean>(false);
const dialogConfirmLoading = ref<boolean>(false);
const refundContentRef = useTemplateRef("refundContentRef");

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<AftersaleRefund>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
  handleGetRefundsStatistics();
};
const handleGetTableList = async () => {
  const params = onGetParams();
  tableLoading.value = true;
  const res = await Order_Api.getOrderAftersaleRefundsList(params);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};

const handleGetRefundsStatistics = async () => {
  const params = onGetParams();
  const res = await Order_Api.getOrderRefundsStatistics(params);
  if (res.Type === 200) {
    totalAmount.value = res.Data.TotalAmount;
  }
};

const onGetParams = () => {
  const params: OrderAftersaleRefundInputDTO = JSON.parse(JSON.stringify(queryParams.value));
  params.RefundTimeStart = dayjs(params.RefundTimeStart).format("YYYY-MM-DD 00:00:00");
  params.RefundTimeEnd = dayjs(params.RefundTimeEnd).format("YYYY-MM-DD 23:59:59");
  return params;
};

const handleExportExcel = async () => {
  const params = {
    ServiceExportCode: "OrderAftersaleRefundReport", //后端
    ExportWay: ExportEnum.ServiceInvoke, // O:Redash, 1：后端
    ExecutingParams: onGetParams(),
    FileName: `退款管理-${Date.now()}.xlsx`,
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } finally {
    exportLoading.value = false;
  }
};

const handleGetOtherText = (text: string, type: string) => {
  if (text) {
    try {
      const item = JSON.parse(text);
      const returnData = item[type];
      if (!returnData) return "";
      if (type === "Remark") {
        return returnData;
      }
      return returnData ? returnData.join("、") : "";
    } catch (error) {
      return "";
    }
  }
  return "";
};

const handleRefundSubmit = async () => {
  const params = refundContentRef.value?.handleSubmit();
  if (!params) return;
  console.log(params);
  let reqFunctionList = [];
  if (params.refundData.length) {
    reqFunctionList.push(Order_Api.refundByOrderDetails(params.refundData));
  }
  if (params.communityData.length) {
    reqFunctionList.push(Community_Api.refundExecCount(params.communityData));
  }
  dialogConfirmLoading.value = true;
  Promise.all(reqFunctionList)
    .then((res) => {
      if (reqFunctionList.length === 1) {
        if (res[0].Type === 200) {
          ElNotification({
            title: "成功",
            message: res[0].Content,
            type: "success",
          });
          // 获取表格数据
          handleGetTableList();
          // 获取退款金额
          handleGetRefundsStatistics();
          showRefundContent.value = false;
        } else {
          ElMessageBox.alert(res[0].Content!, "系统提示", {
            confirmButtonText: "确定",
            type: "warning",
          });
        }
      } else {
        if (res[0].Type === 200 && res[1].Type === 200) {
          ElNotification({
            title: "退款成功",
            type: "success",
          });
          // 获取表格数据
          handleGetTableList();
          // 获取退款金额
          handleGetRefundsStatistics();
        } else if (res[0].Type !== 200) {
          ElNotification({
            title: "非社区退款失败",
            message: res[0].Content,
            type: "error",
          });
        } else if (res[1].Type !== 200) {
          ElNotification({
            title: "社区退款失败",
            message: res[1].Content,
            type: "error",
          });
        }
      }
    })
    .catch((err) => {
      ElMessage.error(err);
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

watch(timeRange, (newVal) => {
  queryParams.value.RefundTimeStart = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.RefundTimeEnd = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  // 获取表格数据
  handleGetTableList();
  // 获取退款金额
  handleGetRefundsStatistics();
});
</script>

<style lang="scss" scoped></style>
