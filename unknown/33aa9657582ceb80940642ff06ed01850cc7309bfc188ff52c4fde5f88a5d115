<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item prop="packType" label="类型">
                <el-select
                  v-model="queryParams.packType"
                  placeholder="请选择"
                  clearable
                  multiple
                  :multiple-limit="1"
                  :empty-values="[undefined, null, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="居家" :value="AdviceMoItemUseScope.Home" />
                  <el-option label="线下" :value="AdviceMoItemUseScope.Community" />
                  <el-option label="院内" :value="AdviceMoItemUseScope.Hospital" />
                </el-select>
              </el-form-item>
              <el-form-item prop="diseasesId" label="适用疾病">
                <el-select
                  v-model="queryParams.diseasesId"
                  placeholder="请选择"
                  filterable
                  clearable
                  :empty-values="[undefined, null, '']"
                  :value-on-clear="() => null"
                >
                  <el-option
                    v-for="item in props.diseaseList"
                    :key="item.Id"
                    :label="item.Key"
                    :value="item.Id!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="keywords">
                <el-input
                  v-model="queryParams.keywords"
                  placeholder="请输入关键字"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="500"
          highlight-current-row
          style="text-align: center; flex: 1"
          @select="handleTableSelect"
          @select-all="handleTableSelect"
        >
          <el-table-column type="selection" width="55" align="center" reserve-selection />
          <el-table-column
            prop="Name"
            label="名称"
            :show-overflow-tooltip="true"
            align="center"
            width="180"
          />
          <el-table-column label="类型" width="80" align="center">
            <template #default="scope">
              {{ ["", "居家", "线下", "院内"][scope.row.Type] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="ExecutDay"
            label="执行天数/天"
            :show-overflow-tooltip="true"
            align="center"
            width="100"
          />
          <el-table-column label="总价（元）" prop="Amount" width="100" align="center" />
          <el-table-column
            label="方案说明"
            show-overflow-tooltip
            prop="TherapistRemark"
            align="center"
            width="180"
          />
          <el-table-column
            label="适用疾病"
            show-overflow-tooltip
            prop="DiseasesName"
            align="center"
            width="120"
          />
          <el-table-column
            label="诊断"
            show-overflow-tooltip
            prop="DiagnosisName"
            align="center"
            width="180"
          />
          <el-table-column
            label="创建时间"
            prop="CreatedTime"
            width="150"
            align="center"
            :formatter="tableDateFormat"
          />
          <el-table-column fixed="right" label="操作" width="80" align="center">
            <template #default="scope">
              <el-button
                link
                size="small"
                type="primary"
                @click="handlePreviewOrEdit(scope.row, true)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog v-model="showReferenceDialog" title="详情" width="1100" destroy-on-close>
      <PlatServerPackContent
        :pack-detail="packDetail"
        :dept-list="deptList"
        :disease-list="props.diseaseList"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showReferenceDialog = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import Content_Api from "@/api/content";
import { GetReferenPageInputDTO, MoItemPackInputDTO, MoItemPackItem } from "@/api/content/types";
import { AdviceMoItemUseScope } from "@/enums/AdviceEnum";
import { useTableConfig } from "@/hooks/useTableConfig";
import { getDeptDivisionsList } from "@/utils/dict";
import PlatServerPackContent from "@/views/platform-resource/servicePackageManagement/components/ServiceContent.vue";

const queryParams = ref<GetReferenPageInputDTO>({
  pageIndex: 1,
  pageSize: 10,
  keywords: "",
  IsEnable: true,
  LoadPackAmount: true,
  packType: null,
  diseasesId: null,
});

const showReferenceDialog = ref<boolean>(false);
const dialogConfirmLoading = ref<boolean>(false);
const packDetail = ref<MoItemPackInputDTO | null>(null);
const deptList = ref<ReadDict[]>([]);

const isOnlyPreview = ref<boolean>(false);
provide("isOnlyPreview", isOnlyPreview);

const { tableLoading, pageData, total, tableRef, tableDateFormat, tableResize, selectedTableIds } =
  useTableConfig<unknown>();

const handleQuery = () => {
  queryParams.value.pageIndex = 1;
  handleGetTableList();
};

const handlePreviewOrEdit = async (row: MoItemPackItem, isPreview: boolean) => {
  isOnlyPreview.value = isPreview;
  const res = await Content_Api.getPackDetailByID({
    packId: row.Id!,
    loadPackAmount: false,
  });
  if (res.Type === 200) {
    packDetail.value = res.Data;
    showReferenceDialog.value = true;
  }
};

const handleTableSelect = (selection: MoItemPackItem[]) => {
  selectedTableIds.value = selection.map((item) => item.Id);
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  Object.keys(copyData).forEach((key) => {
    if (copyData[key] === null) {
      delete copyData[key];
    }
  });
  const res = await Content_Api.getReferenPage(copyData);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

const handleGetDeptList = async () => {
  const list = await getDeptDivisionsList();
  deptList.value = list;
};

onMounted(() => {
  handleGetTableList();
  // 获取科别
  handleGetDeptList();
});
interface Props {
  diseaseList: ReadDict[];
}
const props = defineProps<Props>();

const handleGetSelectIds = (): string[] => {
  return selectedTableIds.value;
};
const clearSelectIds = () => {
  selectedTableIds.value = [];
  tableRef.value?.clearSelection();
};
defineExpose({
  handleGetSelectIds,
  clearSelectIds,
});
</script>

<style lang="scss" scoped></style>
