<template>
  <div class="service-content">
    <el-form
      ref="formRef"
      :disabled="isOnlyPreview"
      :model="formData"
      :rules="rules"
      label-position="right"
      scroll-to-error
    >
      <!-- 类型选择 -->
      <el-row>
        <el-col :span="9">
          <el-form-item label="类型" prop="Type">
            <el-radio-group
              v-model="formData.Type"
              :disabled="!!formData.Id"
              @change="(e) => handleTypeChange(e as number)"
            >
              <el-radio :value="AdviceMoItemUseScope.Home">居家</el-radio>
              <el-radio :value="AdviceMoItemUseScope.Community">线下</el-radio>
              <el-radio :value="AdviceMoItemUseScope.Hospital">院内</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="名称" prop="Name">
            <el-input v-model="formData.Name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="执行天数" prop="ExecutDay">
            <el-input-number v-model="formData.ExecutDay" :min="1" placeholder="请输入执行天数" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 排序和开关 -->
      <el-row>
        <el-col :span="8">
          <el-form-item label="排序" prop="Sort">
            <el-input-number
              v-model="formData.Sort"
              placeholder="请输入执行天数"
              style="width: 120px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否默认推送" prop="IsDefaultPush">
            <el-switch v-model="formData.IsDefaultPush" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否启用" prop="IsEnable">
            <el-switch v-model="formData.IsEnable" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 文本域 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="方案说明" prop="TherapistRemark">
            <el-input
              v-model="formData.TherapistRemark"
              type="textarea"
              :maxlength="200"
              show-word-limit
              placeholder="请输入方案说明"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="部位">
            <el-select-v2
              v-model="formData.Part"
              filterable
              multiple
              collapse-tags
              reserve-keyword
              :max-collapse-tags="5"
              placeholder="请选择部位"
              :props="{ label: 'Key', value: 'Id' }"
              :options="bodyPartList"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="服务包说明" prop="PackExplain">
            <el-input
              v-model="formData.PackExplain"
              type="textarea"
              :maxlength="200"
              show-word-limit
              placeholder="请输入服务包说明"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="服务包备注" prop="Remark">
            <el-input
              v-model="formData.Remark"
              type="textarea"
              :maxlength="200"
              show-word-limit
              placeholder="请输入服务包备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 科别选择 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="科别" prop="DeptIds">
            <el-select v-model="formData.DeptIds" placeholder="请选择" multiple filterable>
              <el-option v-for="item in props.deptList" :label="item.Key" :value="item.Id!" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 适用疾病和西医诊断 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="适用疾病">
            <div class="disease-input">
              <el-tag
                v-for="item in formData.DiseasesData"
                :key="item.Id"
                class="mx-5px"
                closable
                @close="handleRemoveDisease(item)"
              >
                {{ item.Key }}
              </el-tag>
              <el-button v-if="!showDiseaseSelect" type="primary" @click="handleShowDiseaseSelect">
                添加
              </el-button>
              <el-select
                v-else
                ref="diseaseSelectRef"
                v-model="selectedDiseaseId"
                filterable
                placeholder="请选择疾病"
                style="width: 240px"
                @change="handleDiseaseSelect"
                @visible-change="handleVisibleChange"
              >
                <el-option
                  v-for="item in props.diseaseList"
                  :key="item.Id"
                  :label="item.Key"
                  :value="item.Id!"
                />
              </el-select>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="西医诊断">
            <div class="diagnosis-input">
              <el-tag
                v-for="item in westernMedicineDiagnosis"
                :key="item.DiagnoseCode"
                class="mx-5px"
                closable
                @close="handleRemoveWesternDiagnosis(item)"
              >
                {{ item.DiagnoseName }}
              </el-tag>
              <el-button
                v-if="!showWesternDiagnosisSelect"
                type="primary"
                @click="handleShowWesternDiagnosisSelect"
              >
                添加
              </el-button>
              <el-select
                v-else
                ref="westernDiagnosisSelectRef"
                v-model="selectedWesternDiagnosisIds"
                multiple
                filterable
                remote
                :remote-method="handleWesternDiagnosisSearch"
                placeholder="请选择西医诊断"
                style="width: 240px"
                @blur="handleWesternDiagnosisBlur"
                @visible-change="handleWesternDiagnosisVisibleChange"
              >
                <el-option
                  v-for="item in westernDiagnosisList"
                  :key="item.Id"
                  :label="item.Key"
                  :value="item.Value!"
                />
              </el-select>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 中医诊断 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="中医诊断">
            <div class="diagnosis-input">
              <el-tag
                v-for="item in tCMDiagnosis"
                :key="item.DiagnoseCode"
                class="mx-5px"
                closable
                @close="handleRemoveTCMDiagnosis(item)"
              >
                {{ item.DiagnoseName }}
              </el-tag>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="中医病证">
            <el-select
              v-model="chineseMedicine.diseaseId"
              filterable
              remote
              :remote-method="handleCMDiseaseSearch"
              placeholder="请选择中医病证"
              @change="handleCMDiseaseChange"
            >
              <el-option
                v-for="item in cmdDiseasesList"
                :key="item.Id"
                :label="item.Key"
                :value="item.Value!"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="中医症候">
            <el-select v-model="chineseMedicine.symptom" placeholder="请选择中医症候" filterable>
              <el-option v-for="item in symptomList" :label="item.Key" :value="item.Value!" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleAddCMDisease">添加</el-button>
        </el-col>
      </el-row>
      <!-- 医嘱表格 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="medical-advice">
            <div class="advice-header">
              <el-select
                v-model="medicalAdvice"
                placeholder="请选择医嘱"
                filterable
                style="width: 280px"
              >
                <el-option v-for="item in medicalAdviceList" :label="item.Name" :value="item.Id" />
              </el-select>
              <el-button type="primary" class="ml-10px" @click="handleAddMedicalAdvice">
                添加
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      :data="formData.MoItemGroupDetails"
      style="width: 100%; margin-top: 10px"
      align="center"
      border
    >
      <el-table-column prop="MoName" label="医嘱名称" align="center" fixed="left" />
      <el-table-column prop="frequency" label="频次" align="center">
        <template #default="scope">
          <span v-if="scope.row.FreqDay && scope.row.Freq">
            {{ scope.row.FreqDay }}天 {{ scope.row.Freq }}次
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="MoDay" label="天数" align="center" />
      <el-table-column prop="TotalCount" label="总次数" align="center" />
      <el-table-column label="穴位" align="center" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.PackAcupoints && scope.row.PackAcupoints.length > 0">
            {{ scope.row.PackAcupoints.map((item: AcuPointInfo) => item.Name).join(",") }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="training" label="训练动作" align="center" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.PackActions && scope.row.PackActions.length > 0">
            {{ scope.row.PackActions.map((item: MoItemAction) => item.Name).join(",") }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="Price" label="单价" align="center" />
      <el-table-column prop="TotalPrice" label="金额" align="center" />
      <el-table-column label="操作" align="center" width="180px" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleSetAdvicePrice(scope.row)">
            {{ isOnlyPreview ? "查看价格" : "修改价格" }}
          </el-button>
          <el-button link type="primary" @click="handleEditAdvice(scope.row)">
            {{ isOnlyPreview ? "查看" : "编辑" }}
          </el-button>
          <el-button
            link
            type="danger"
            :disabled="isOnlyPreview || !!formData.Id"
            @click="handleDeleteMedicalAdvice(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="total-amount">
      <span>合计金额：{{ totalAmount }}元</span>
    </div>
    <el-dialog
      v-model="showDialog.general"
      :title="medicalAdviceDialogTitle"
      width="50%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <GeneralAdviceContent
        ref="generalAdviceContentRef"
        :mo-item-id="moItemId"
        :info="adviceMoItemInfo"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.general = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleGetNextData('generalAdviceContentRef')"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDialog.acupoint"
      :title="medicalAdviceDialogTitle"
      width="50%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <AcupointAdviceContent
        ref="acupointAdviceContentRef"
        :mo-item-id="moItemId"
        :info="adviceMoItemInfo"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.acupoint = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleGetNextData('acupointAdviceContentRef')"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDialog.scale"
      :title="medicalAdviceDialogTitle"
      width="50%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <ScaleAdviceContent
        ref="scaleAdviceContentRef"
        :mo-item-id="moItemId"
        :info="adviceMoItemInfo"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.scale = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleGetNextData('scaleAdviceContentRef')"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDialog.consumables"
      :title="medicalAdviceDialogTitle"
      width="50%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <ConsumablesAdviceContent
        ref="consumablesAdviceContentRef"
        :mo-item-id="moItemId"
        :info="adviceMoItemInfo"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.consumables = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleGetNextData('consumablesAdviceContentRef')"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDialog.missionary"
      :title="medicalAdviceDialogTitle"
      width="50%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <MissionaryAdviceContent
        ref="missionaryAdviceContentRef"
        :mo-item-id="moItemId"
        :info="adviceMoItemInfo"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.missionary = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleGetNextData('missionaryAdviceContentRef')"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showDialog.setPrice"
      title="设置价格"
      width="50%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <SetPriceContent ref="setPriceContentRef" :mo-item-id="moItemId" :info="priceInfo" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.setPrice = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleGetSetPrice"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage, ElMessageBox } from "element-plus";
import { AdviceMoItemMethod, AdviceMoItemUseScope } from "@/enums/AdviceEnum";
import {
  getWesternDiagnosisList,
  getCMDiseasesDictList,
  getCMDiseasesSymptomList,
  getBodyPartList,
} from "@/utils/dict";
import Content_Api from "@/api/content";
import {
  MoItemGroupItem,
  MoItemPageData,
  MoItemPackInputDTO,
  MoItemAction,
  PageBaseChargeItemType,
} from "@/api/content/types";
import { calculateMedicalAdviceTotalPrice } from "@/utils/serviceUtils";
import GeneralAdviceContent from "./GeneralAdviceContent.vue";
import AcupointAdviceContent from "./AcupointAdviceContent.vue";
import ScaleAdviceContent from "./ScaleAdviceContent.vue";
import ConsumablesAdviceContent from "./ConsumablesAdviceContent.vue";
import MissionaryAdviceContent from "./MissionaryAdviceContent.vue";
import SetPriceContent from "./SetPriceContent.vue";
import { calculateAmount } from "@/utils";
interface PageUpdateMoItemPackInputDTO extends Omit<MoItemPackInputDTO, "DiseasesData"> {
  DiseasesData?: ReadDict[];
}
const isOnlyPreview = inject("isOnlyPreview") as Ref<boolean>;
const formRef = ref<FormInstance>();
const generalAdviceContentRef = ref<InstanceType<typeof GeneralAdviceContent> | null>(null);
const acupointAdviceContentRef = ref<InstanceType<typeof AcupointAdviceContent> | null>(null);
const scaleAdviceContentRef = ref<InstanceType<typeof ScaleAdviceContent> | null>(null);
const consumablesAdviceContentRef = ref<InstanceType<typeof ConsumablesAdviceContent> | null>(null);
const missionaryAdviceContentRef = ref<InstanceType<typeof MissionaryAdviceContent> | null>(null);
const setPriceContentRef = ref<InstanceType<typeof SetPriceContent> | null>(null);
// 疾病选择相关
const showDiseaseSelect = ref<boolean>(false);
const diseaseSelectRef = ref();
const selectedDiseaseId = ref<string>("");
const medicalAdviceDialogTitle = ref<string>("");
const medicalAdviceList = ref<MoItemPageData[]>([]);
const formData = reactive<PageUpdateMoItemPackInputDTO>({
  Type: 1,
  Name: "",
  ExecutDay: 1,
  Sort: 0,
  IsDefaultPush: false,
  IsEnable: true,
  TherapistRemark: "",
  PackExplain: "",
  Remark: "",
  DeptIds: [],
  DiseasesData: [] as ReadDict[],
  MoItemGroupDetails: [] as MoItemGroupItem[],
  Part: [],
});
const dialogConfirmLoading = ref<boolean>(false);
const medicalAdvice = ref<string>("");
const priceInfo = ref<PageBaseChargeItemType[]>([]);
const showDialog = reactive({
  general: false,
  acupoint: false,
  scale: false,
  consumables: false,
  missionary: false,
  setPrice: false,
});
const moItemId = ref<string>("");
const adviceMoItemInfo = ref<MoItemGroupItem | null>(null);

// 西医诊断相关
const showWesternDiagnosisSelect = ref(false);
const westernDiagnosisSelectRef = ref();
const selectedWesternDiagnosisIds = ref<string[]>([]);
const westernDiagnosisList = ref<ReadDict[]>([]);
// 部位相关
const bodyPartList = ref<ReadDict[]>([]);

// 中医诊断相关
const chineseMedicine = reactive({
  diseaseId: "", // 中医病证ID
  symptom: "", // 中医症候
});
const cmdDiseasesList = ref<ReadDict[]>([]);
const symptomList = ref<ReadDict[]>([]);

// 诊断相关数据
const westernMedicineDiagnosis = ref<BaseDiagnosis[]>([]);
const tCMDiagnosis = ref<BaseDiagnosis[]>([]);

// 表单验证规则
const rules = reactive<FormRules>({
  Name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  ExecutDay: [
    { required: true, message: "请输入执行天数", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        // 获取医嘱中最大的MoDay
        const maxMoDay = formData.MoItemGroupDetails.reduce(
          (max, item) => Math.max(max, item.MoDay),
          0
        );
        if (value < maxMoDay) {
          callback(new Error("执行天数不能小于医嘱中最大的执行天数"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
});

// 计算合计金额
const totalAmount = computed(() => {
  const totals = formData.MoItemGroupDetails.map((item) => item.TotalPrice || 0);
  return calculateAmount(totals, "+");
});

// 显示疾病选择器
const handleShowDiseaseSelect = async () => {
  showDiseaseSelect.value = true;
  await nextTick();
  const select = diseaseSelectRef.value;
  select?.focus();
  // 手动触发下拉框显示
  if (select) {
    select.expanded = true;
  }
};
// 处理下拉框显示状态变化
const handleVisibleChange = (visible: boolean) => {
  if (!visible) {
    showDiseaseSelect.value = false;
  }
};
/** 加载部位数据 */
const loadBodyPartList = async () => {
  const list = await getBodyPartList({
    PageSize: 9999,
    Key: "",
    IsEnabled: true,
    IsPublish: true,
  });
  bodyPartList.value = list;
};

// 处理疾病选择
const handleDiseaseSelect = (diseaseId: string) => {
  const disease = props.diseaseList.find((item) => item.Id === diseaseId);
  if (disease && !formData.DiseasesData!.some((item) => item.Id === disease.Id)) {
    formData.DiseasesData!.push(disease);
  }
  showDiseaseSelect.value = false;
  selectedDiseaseId.value = "";
};

// 移除已选疾病
const handleRemoveDisease = (disease: ReadDict) => {
  formData.DiseasesData = formData.DiseasesData!.filter((item) => item.Id !== disease.Id);
};

// 初始化加载西医诊断数据
const loadWesternDiagnosisList = async () => {
  const list = await getWesternDiagnosisList({
    PageSize: 30,
    Key: "",
    IsEnabled: true,
  });
  westernDiagnosisList.value = list;
};

// 显示西医诊断选择器
const handleShowWesternDiagnosisSelect = async () => {
  showWesternDiagnosisSelect.value = true;
  await loadWesternDiagnosisList();
  await nextTick();
  const select = westernDiagnosisSelectRef.value;
  select?.focus();
  if (select) {
    select.expanded = true;
  }
};

// 处理西医诊断搜索
const handleWesternDiagnosisSearch = async (query: string) => {
  if (query) {
    const list = await getWesternDiagnosisList({
      PageSize: 5000,
      Key: query,
      IsEnabled: true,
      IsPublish: true,
    });
    westernDiagnosisList.value = list;
  } else {
    await loadWesternDiagnosisList();
  }
};

// 处理西医诊断选择框失焦
const handleWesternDiagnosisBlur = () => {
  const selectedItems = selectedWesternDiagnosisIds.value
    .map((id) => westernDiagnosisList.value.find((item) => item.Value === id))
    .filter((item): item is ReadDict => item !== undefined);

  // 转换为BaseDiagnosis格式并添加
  const newDiagnoses = selectedItems.map((item) => ({
    DiagnoseTypeName: "西医诊断",
    DiagnoseName: item.Key!,
    DiagnoseCode: item.Value!,
    IsMain: westernMedicineDiagnosis.value.length === 0,
  }));

  // 过滤掉已存在的诊断
  const uniqueDiagnoses = newDiagnoses.filter(
    (newItem) =>
      !westernMedicineDiagnosis.value.some(
        (existingItem) => existingItem.DiagnoseCode === newItem.DiagnoseCode
      )
  );

  // 添加诊断
  westernMedicineDiagnosis.value.push(...uniqueDiagnoses);

  // 清空选择并隐藏select
  selectedWesternDiagnosisIds.value = [];
  showWesternDiagnosisSelect.value = false;
};

// 处理西医诊断下拉框显示状态变化
const handleWesternDiagnosisVisibleChange = (visible: boolean) => {
  if (!visible) {
    showWesternDiagnosisSelect.value = false;
  }
};

// 移除已选西医诊断
const handleRemoveWesternDiagnosis = (diagnosis: BaseDiagnosis) => {
  westernMedicineDiagnosis.value = westernMedicineDiagnosis.value.filter(
    (item) => item.DiagnoseCode !== diagnosis.DiagnoseCode
  );
};

const handleRemoveTCMDiagnosis = (diagnosis: BaseDiagnosis) => {
  tCMDiagnosis.value = tCMDiagnosis.value.filter(
    (item) => item.DiagnoseCode !== diagnosis.DiagnoseCode
  );
};

// 加载中医病证数据
const loadCMDiseasesList = async () => {
  const list = await getCMDiseasesDictList({
    PageSize: 50,
    Key: "",
    IsEnabled: true,
    IsPublish: true,
  });
  cmdDiseasesList.value = list;
};

// 处理中医病证搜索
const handleCMDiseaseSearch = async (query: string) => {
  if (query) {
    const list = await getCMDiseasesDictList({
      PageSize: 500,
      Key: query,
      IsEnabled: true,
      IsPublish: true,
    });
    cmdDiseasesList.value = list;
  } else {
    await loadCMDiseasesList();
  }
};

// 处理中医病证选择
const handleCMDiseaseChange = async (value: string) => {
  // 找出源数据 items
  const item = cmdDiseasesList.value.find((item) => item.Value === value);
  let rules: {
    Field: string;
    Value: string;
    Operate: number;
  }[] = [];
  if (item?.DictItemRelateds && item?.DictItemRelateds.length) {
    var groups: {
      Field: string;
      Value: string;
      Operate: number;
    }[] = [];
    item?.DictItemRelateds.forEach((item) => {
      groups.push({
        Field: "Id",
        Value: item.DictItemIdA,
        Operate: 3,
      });
    });
    rules = groups;
  } else {
    rules = [];
  }
  chineseMedicine.symptom = "";
  const list = await getCMDiseasesSymptomList({
    PageSize: 9999,
    Key: "",
    IsEnabled: true,
    IsPublish: true,
    Rules: rules,
  });
  symptomList.value = list;
};
/** 添加中医诊断 */
const handleAddCMDisease = () => {
  if (!chineseMedicine.symptom || !chineseMedicine.diseaseId) {
    ElMessage.warning("请选择中医病证和中医症候");
    return;
  }
  // 获取病症和症候数据
  const disease = cmdDiseasesList.value.find((item) => item.Value === chineseMedicine.diseaseId);
  const symptom = symptomList.value.find((item) => item.Value === chineseMedicine.symptom);
  const obj = {
    DiagnoseTypeName: "中医诊断",
    DiagnoseName: disease?.Key + "|" + symptom?.Key,
    DiagnoseCode: disease?.Value + "|" + symptom?.Value,
    IsMain: tCMDiagnosis.value.length === 1,
  };
  // 如果存在就不添加
  if (tCMDiagnosis.value.find((item) => item.DiagnoseCode === obj.DiagnoseCode)) return;
  tCMDiagnosis.value.push(obj);
};
/** 加载医嘱数据 */
const loadMedicalAdviceList = async () => {
  try {
    const res = await Content_Api.getMoItemPageData({
      page: 1,
      pageSize: 9999,
      isOrderBytBeReferenceNum: true,
      isEnable: true,
      moItemUseScope: formData.Type === 1 || formData.Type === 2 ? [1, 2] : [3],
    });
    if (res.Type === 200) {
      medicalAdviceList.value = res.Data.Data;
    } else {
      medicalAdviceList.value = [];
      ElMessage.error(res.Content);
    }
  } catch (error) {
    ElMessage.error(JSON.stringify(error));
    medicalAdviceList.value = [];
  }
};
/** 设置下一页数据 */
const handleSendInfoToNextPageData = (item: MoItemPageData) => {
  adviceMoItemInfo.value = {
    MoItemId: item.Id,
    MoName: item.Name,
    MoRemark: item.Remark,
    MoDay: item.DefaultMoDay || 1,
    Freq: 1,
    TotalCount: 1,
    Price: item.MoItemAmount,
    FreqDay: 1,
    IsSpecialFreq: item.IsSpecialFreq,
    MoItemMethod: item.MoItemMethod,
    MoItemUseScope: item.MoItemUseScope,
    LogisticsDay: item.LogisticsDay,
    ChargeMode: item.ChargeMode,
    ChargeItem: item.ChargeItems[0],
    MaxDay: item.MaxDay,
    MinDay: item.MinDay,
  };
};

/** 验证医嘱选择 */
const validateMedicalAdvice = (item: MoItemPageData) => {
  if (!medicalAdvice.value) {
    ElMessage.error("请选择医嘱");
    return false;
  }

  const moItemUseScope = item.MoItemUseScope;
  if (
    formData.Type === AdviceMoItemUseScope.Home ||
    formData.Type === AdviceMoItemUseScope.Community
  ) {
    if (moItemUseScope !== 1 && moItemUseScope !== 2) {
      ElMessage.error("请选择使用范围为居家或线下的医嘱");
      return false;
    }
  } else if (moItemUseScope !== AdviceMoItemUseScope.Hospital) {
    ElMessage.error("请选择使用范围为院内的医嘱");
    return false;
  }

  if (formData.MoItemGroupDetails.some((s) => s.MoItemId === item.Id)) {
    ElMessage.error("已经选择的医嘱不能再添加");
    return false;
  }
  return true;
};

/** 处理医嘱方法选择 */
const handleMedicalAdviceMethod = (item: MoItemPageData) => {
  medicalAdviceDialogTitle.value = item.Name;
  moItemId.value = item.Id;

  switch (item.MoItemMethod) {
    case AdviceMoItemMethod.General:
      handleSendInfoToNextPageData(item);
      showDialog.general = true;
      break;
    case AdviceMoItemMethod.Acupoint:
      handleSendInfoToNextPageData(item);
      showDialog.acupoint = true;
      break;
    case AdviceMoItemMethod.Consultation:
      handleConsultationAdvice(item);
      break;
    case AdviceMoItemMethod.Evaluation:
      handleSendInfoToNextPageData(item);
      showDialog.scale = true;
      break;
    case AdviceMoItemMethod.Equipment:
      ElMessage.info("暂未支持辅具类医嘱");
      break;
    case AdviceMoItemMethod.Consumables:
      handleSendInfoToNextPageData(item);
      showDialog.consumables = true;
      break;
    case AdviceMoItemMethod.Education:
      handleSendInfoToNextPageData(item);
      showDialog.missionary = true;
      break;
  }
};

/** 处理咨询类医嘱 */
const handleConsultationAdvice = (item: MoItemPageData) => {
  const obj: MoItemGroupItem = {
    MoDay: 1,
    Freq: 1,
    TotalCount: 1,
    FreqDay: 1,
    Price: item.MoItemAmount,
    MoItemId: item.Id,
    MoName: item.Name,
    ChargeMode: item.ChargeMode,
    Part: 1,
    MoItemMethod: item.MoItemMethod,
    MoRemark: item.Remark,
    IsSpecialFreq: item.IsSpecialFreq,
    MoItemUseScope: item.MoItemUseScope,
    LogisticsDay: item.LogisticsDay,
    ChargeItem: item.ChargeItems[0],
  };
  obj.TotalPrice = calculateMedicalAdviceTotalPrice(obj);
  handleTransformServicePackageData(obj);
};

/** 添加医嘱 */
const handleAddMedicalAdvice = () => {
  const item = medicalAdviceList.value.find((item) => item.Id === medicalAdvice.value)!;
  const isExistHospital = formData.MoItemGroupDetails.some(
    (s) => s.MoItemUseScope === AdviceMoItemUseScope.Hospital
  );
  if (formData.MoItemGroupDetails.length) {
    if (
      (isExistHospital && formData.Type !== AdviceMoItemUseScope.Hospital) ||
      (!isExistHospital && formData.Type === AdviceMoItemUseScope.Hospital)
    ) {
      ElMessage.error("院内医嘱不能和居家、线下医嘱同时存在");
      return;
    }
  }
  if (!validateMedicalAdvice(item)) return;
  handleMedicalAdviceMethod(item);
};
/** 编辑医嘱 */
const handleEditAdvice = (item: MoItemGroupItem) => {
  moItemId.value = item.MoItemId;
  adviceMoItemInfo.value = item;
  switch (item.MoItemMethod) {
    case AdviceMoItemMethod.General:
      showDialog.general = true;
      break;
    case AdviceMoItemMethod.Acupoint:
      showDialog.acupoint = true;
      break;
    case AdviceMoItemMethod.Consultation:
      break;
    case AdviceMoItemMethod.Evaluation:
      showDialog.scale = true;
      break;
    case AdviceMoItemMethod.Equipment:
      ElMessage.info("暂未支持辅具类医嘱");
      break;
    case AdviceMoItemMethod.Consumables:
      showDialog.consumables = true;
      break;
    case AdviceMoItemMethod.Education:
      showDialog.missionary = true;
      break;
  }
};

// 计算执行天数
const handleSetExecutDay = () => {
  // 重新计算执行天数 将MoItemGroupDetails中MoItemMethod != 2的筛选出来之后，获取最大的MoDay
  const maxMoDay = formData.MoItemGroupDetails.filter((item) => item.MoItemMethod !== 2).reduce(
    (max, item) => Math.max(max, item.MoDay),
    0
  );
  formData.ExecutDay = maxMoDay;
};
// 重新更新咨询类医嘱的天数（取所有医嘱的最大天数）
const handleSetAdvisoryMoItemInfo = () => {
  // 如果有MoItemMethod == 2的数据 将MoItemGroupDetails中MoItemMethod ！= 2的筛选出来之后，获取最大的MoDay 然后赋值给MoItemMethod == 2的数据
  const advisoryMoItem = formData.MoItemGroupDetails.find((item) => item.MoItemMethod === 2);
  let maxMoDay: number = 1;
  if (formData.MoItemGroupDetails.filter((item) => item.MoItemMethod !== 2).length) {
    maxMoDay = formData.MoItemGroupDetails.filter((item) => item.MoItemMethod !== 2).reduce(
      (max, item) => Math.max(max, item.MoDay),
      0
    );
  }
  if (advisoryMoItem) {
    advisoryMoItem.MoDay = maxMoDay;
  }
};
// 对新增或者编辑的服务包数据进行转化为该页面的数据结构
const handleTransformServicePackageData = (data: MoItemGroupItem) => {
  // 看MoItemGroupDetails中是否存在 some
  const index = formData.MoItemGroupDetails.findIndex((item) => item.MoItemId === data.MoItemId);
  if (index !== -1) {
    // 如果存在就更新（直接把 data 覆盖源数据）
    formData.MoItemGroupDetails[index] = data;
  } else {
    // 不存在就添加
    formData.MoItemGroupDetails.push(data);
  }
  // 重新计算执行天数
  handleSetExecutDay();
  // 重新更新咨询类医嘱的天数（取所有医嘱的最大天数）
  handleSetAdvisoryMoItemInfo();
};
const dialogRefs = {
  generalAdviceContentRef: { ref: generalAdviceContentRef, dialog: "general" },
  acupointAdviceContentRef: { ref: acupointAdviceContentRef, dialog: "acupoint" },
  scaleAdviceContentRef: { ref: scaleAdviceContentRef, dialog: "scale" },
  consumablesAdviceContentRef: { ref: consumablesAdviceContentRef, dialog: "consumables" },
  missionaryAdviceContentRef: { ref: missionaryAdviceContentRef, dialog: "missionary" },
} as const;

const handleGetNextData = async (refName: keyof typeof dialogRefs) => {
  const { ref, dialog } = dialogRefs[refName];
  const params = await ref.value?.handleSubmit();

  if (params) {
    params.TotalPrice = calculateMedicalAdviceTotalPrice(params);
    handleTransformServicePackageData(params);
    showDialog[dialog] = false;
  }
};
const handleDeleteMedicalAdvice = (row: MoItemGroupItem) => {
  ElMessageBox.confirm("确定要删除该医嘱吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 从数组中移除指定医嘱
      formData.MoItemGroupDetails = formData.MoItemGroupDetails.filter(
        (item) => item.MoItemId !== row.MoItemId
      );
      // 重新计算执行天数
      handleSetExecutDay();
      // 重新更新咨询类医嘱的天数
      handleSetAdvisoryMoItemInfo();
      ElMessage.success("删除成功");
    })
    .catch(() => {
      // 取消删除操作
    });
};
const handleTransformServicePackageSendData = (
  data: PageUpdateMoItemPackInputDTO | null
): MoItemPackInputDTO | null => {
  if (!data) return null;
  const copyData: PageUpdateMoItemPackInputDTO = JSON.parse(JSON.stringify(data));
  const moItemGroupDetails = copyData.MoItemGroupDetails.map((element) => {
    if (element.PackActions && element.PackActions.length) {
      element.PackActions.forEach((item) => {
        if (item.InstrumentParameter) {
          const instrumentParameter = JSON.parse(item.InstrumentParameter);
          instrumentParameter.forEach((item: BaseInstrumentParameter) => {
            delete item.NotShow;
            delete item.OptionValue;
          });
          item.InstrumentParameter = JSON.stringify(instrumentParameter);
        }
      });
    }
    if (!element.ChargeItem) {
      element.ChargeItem = {
        MoItemId: element.MoItemId,
        PackChargeItemDetails: [],
      };
    }
    return element;
  });
  const newData: MoItemPackInputDTO = {
    Type: copyData.Type,
    Name: copyData.Name,
    ExecutDay: copyData.ExecutDay,
    TherapistRemark: copyData.TherapistRemark,
    Remark: copyData.Remark,
    PackExplain: copyData.PackExplain,
    DeptIds: copyData.DeptIds,
    IsDefaultPush: copyData.IsDefaultPush,
    IsEnable: copyData.IsEnable,
    DiseasesIds: copyData.DiseasesData?.map((item) => item.Id!),
    MoItemGroupDetails: moItemGroupDetails,
    Sort: copyData.Sort,
    MoItemPackDiagnosis: [...westernMedicineDiagnosis.value, ...tCMDiagnosis.value],
    Part: copyData.Part,
  };
  if (copyData.Id) {
    newData.PackId = copyData.Id; // PackId是当修改的时候传递的
  }
  return newData;
};
/** 修改医嘱价格 */
const handleSetAdvicePrice = (row: MoItemGroupItem) => {
  moItemId.value = row.MoItemId;
  priceInfo.value = JSON.parse(JSON.stringify(row.ChargeItem?.PackChargeItemDetails || []));
  showDialog.setPrice = true;
};
/** 确定修改价格 */
const handleGetSetPrice = () => {
  const data = setPriceContentRef.value?.handleSubmit() || [];
  if (data) {
    const row = formData.MoItemGroupDetails.find((item) => item.MoItemId === moItemId.value);
    if (row) {
      row.ChargeItem!.PackChargeItemDetails = data;
      // 重新计算单价
      row.Price = calculateAmount(
        data.map((s) => calculateAmount([s.Price, s.Quantity!], "*")),
        "+"
      );
      // 重新计算总价
      row.TotalPrice = calculateMedicalAdviceTotalPrice(row);
    }
  }
  showDialog.setPrice = false;
  moItemId.value = "";
  priceInfo.value = [];
};
/** 类型改变 */
const handleTypeChange = (e: number) => {
  medicalAdvice.value = "";
  // 重新加载医嘱数据
  loadMedicalAdviceList();
};
const validateForm = () => {
  if (!formRef.value) return false;
  if (!formData.MoItemGroupDetails || !formData.MoItemGroupDetails.length) {
    ElMessage.error("请添加医嘱");
    return false;
  }
  if (!tCMDiagnosis.value.length && !westernMedicineDiagnosis.value.length) {
    ElMessage.error("请添加西医诊断或中医诊断");
    return false;
  }
  const isHaveHospital = formData.MoItemGroupDetails.some((s) => s.MoItemUseScope === 3);
  if ((isHaveHospital && formData.Type !== 3) || (!isHaveHospital && formData.Type === 3)) {
    ElMessage.warning("医嘱类型和服务包类型不符");
    return false;
  }
  return true;
};

// 表单提交
const handleSubmit = async (): Promise<MoItemPackInputDTO | null> => {
  if (!validateForm()) return null;
  try {
    await formRef.value?.validate();
    return handleTransformServicePackageSendData(formData);
  } catch {
    return null;
  }
};
const handleProcessPackDetail = (
  data: MoItemPackInputDTO | null
): PageUpdateMoItemPackInputDTO | null => {
  if (!data) return null;
  const copyData: PageUpdateMoItemPackInputDTO = JSON.parse(JSON.stringify(data));
  if (copyData.MoItemPackDiagnosis && copyData.MoItemPackDiagnosis.length) {
    copyData.MoItemPackDiagnosis.forEach((v) => {
      if (v.DiagnoseTypeName === "中医诊断") {
        tCMDiagnosis.value.push(v);
      } else if (v.DiagnoseTypeName === "西医诊断") {
        westernMedicineDiagnosis.value.push(v);
      }
    });
  }
  // 将疾病转化为数组
  copyData.DiseasesData = copyData.DiseasesData || [];
  // 计算每个医嘱的单价
  copyData.MoItemGroupDetails.forEach((s) => {
    let signPrice = 0;
    if (s.ChargeItem && s.ChargeItem.PackChargeItemDetails?.length) {
      s.ChargeItem.PackChargeItemDetails.forEach((j) => {
        signPrice += j.Amount;
      });
      s.Price = signPrice;
    } else {
      s.Price = 0;
    }
    s.TotalPrice = calculateMedicalAdviceTotalPrice(s);
  });
  copyData.Part = copyData.Part || [];
  return copyData;
};
interface Props {
  deptList: ReadDict[];
  diseaseList: ReadDict[];
  packDetail: MoItemPackInputDTO | null;
}
const props = defineProps<Props>();
watch(
  () => props.packDetail,
  (newVal) => {
    const data = handleProcessPackDetail(newVal);
    if (data) {
      Object.assign(formData, data);
    }
  },
  { immediate: true }
);
defineExpose({
  handleSubmit,
});
// 初始化时加载数据
onMounted(() => {
  loadWesternDiagnosisList();
  loadCMDiseasesList();
  // 加载医嘱数据
  loadMedicalAdviceList();
  // 加载部位数据
  loadBodyPartList();
});
</script>

<style lang="scss" scoped>
.service-content {
  padding: 20px;
  height: 500px;
  overflow-y: auto;
  width: 100%;
}

.total-amount {
  margin-top: 10px;
  text-align: left;
  font-weight: bold;

  span {
    margin-left: 20px;
  }
}

.disease-input {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  min-height: 32px;
}

.mx-5px {
  margin: 0 5px;
}
</style>
