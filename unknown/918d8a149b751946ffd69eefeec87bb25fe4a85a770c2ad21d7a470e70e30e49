<template>
  <div>
    <el-form ref="formRef" :model="formData">
      <el-row :gutter="20" class="mb-4">
        <el-col :span="8">
          <el-form-item
            label="收费项目"
            prop="Id"
            :rules="[{ required: true, message: '请选择收费项目' }]"
          >
            <el-select v-model="formData.Id" placeholder="请选择收费项目" style="width: 100%">
              <el-option
                v-for="item in chargeItemList"
                :key="item.Id"
                :label="item.Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="数量"
            prop="Quantity"
            :rules="[{ required: true, message: '请输入数量' }]"
          >
            <el-input-number v-model="formData.Quantity" :min="0" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" :disabled="isPreview" @click="handleAdd">添加</el-button>
        </el-col>
      </el-row>
    </el-form>

    <el-table :data="tableData" style="width: 100%" border>
      <el-table-column prop="Name" label="名称" align="center" />
      <el-table-column prop="Code" label="编码" align="center" />
      <el-table-column prop="Quantity" label="数量" align="center" />
      <el-table-column prop="Unit" label="单位" align="center" />
      <el-table-column label="价格" align="center">
        <template #default="{ row }">
          {{ (row.Price * row.Quantity).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100">
        <template #default="{ row }">
          <el-button type="danger" link :disabled="isPreview" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="mt-4">
      <span>合计：</span>
      <span>{{ totalAmount }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage, FormInstance } from "element-plus";
import ContentApi from "@/api/content";
const isPreview = inject("isPreview") as Ref<boolean>;

export interface CostTableProps extends Omit<BaseChargeItemType, "Quantity"> {
  Quantity?: number;
}

interface Props {
  chargeItems: CostTableProps[];
}

// Props定义
const props = defineProps<Props>();

// 状态定义
const formRef = ref<FormInstance>();
const formData = ref({
  Id: undefined,
  Quantity: undefined,
});
const chargeItemList = ref<BaseChargeItemType[]>([]);
const tableData = ref<CostTableProps[]>([]);

// 计算属性
const totalAmount = computed(() => {
  return tableData.value
    .reduce((sum, item) => sum + (item.Price || 0) * (item.Quantity || 0), 0)
    .toFixed(2);
});

const handleCostFinish = (): CostTableProps[] => {
  return tableData.value;
};

// 方法暴露
defineExpose({
  handleCostFinish,
});

// 监听props变化
watch(
  () => props.chargeItems,
  (newVal) => {
    tableData.value = newVal;
  },
  {
    immediate: true,
  }
);

// 获取收费项目列表
const fetchChargeItemList = async () => {
  try {
    const res = await ContentApi.getChargeItemPageData({
      page: 1,
      pageSize: 50,
      isEnable: true,
      keywords: "",
      type: 3,
    });
    chargeItemList.value = res.Data.Data || [];
  } catch (error) {
    console.error("获取收费项目失败:", error);
  }
};

// 添加收费项目
const handleAdd = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    if (tableData.value.some((item) => item.Id === formData.value.Id)) {
      ElMessage.error("收费项目已存在");
      return;
    }
    const chargeItem = chargeItemList.value.find((item) => item.Id === formData.value.Id);
    if (chargeItem) {
      const newItem = {
        ...chargeItem,
        Quantity: formData.value.Quantity,
      };
      tableData.value.push(newItem);
      formRef.value.resetFields();
    }
  } catch (error) {
    console.error("表单验证失败:", error);
  }
};

// 删除收费项目
const handleDelete = (record: CostTableProps) => {
  tableData.value = tableData.value.filter((item) => item.Id !== record.Id);
};

// 生命周期
onMounted(() => {
  fetchChargeItemList();
});
</script>

<style lang="scss" scoped>
.mb-4 {
  margin-bottom: 16px;
}
.mt-4 {
  margin-top: 16px;
}
</style>
