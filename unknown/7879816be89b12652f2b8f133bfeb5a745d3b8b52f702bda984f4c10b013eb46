interface OptionItem {
  Key: string;
  Value: number;
}

const fOptionValue: OptionItem[] = [];
for (let index = 10; index <= 32; index++) {
  fOptionValue.push({
    Key: index + "HZ",
    Value: index,
  });
}
const timesOptionValue: OptionItem[] = [];
const ploadOptionValue: OptionItem[] = [];
const levelOptionValue: OptionItem[] = [];
const difficultOptionValue: OptionItem[] = [];
const countOptionValue: OptionItem[] = [];
for (let index = 5; index <= 200; index++) {
  timesOptionValue.push({
    Key: index + "次",
    Value: index,
  });
}
for (let index = 3; index <= 200; index++) {
  ploadOptionValue.push({
    Key: index + "cmH2O",
    Value: index,
  });
}
for (let index = 1; index <= 10; index++) {
  levelOptionValue.push({
    Key: index + "档",
    Value: index,
  });
}
for (let index = 1; index <= 5; index++) {
  difficultOptionValue.push({
    Key: index + "星",
    Value: index,
  });
}
for (let index = 1; index <= 10; index++) {
  countOptionValue.push({
    Key: index + "次",
    Value: index,
  });
}

interface SkParameterType {
  times: OptionItem[];
  count: OptionItem[];
  trainType: OptionItem[];
  level: OptionItem[];
  f: OptionItem[];
  pload: OptionItem[];
  difficult: OptionItem[];
}

export const getSkParameter = (MFType = 3): SkParameterType => {
  const obj: SkParameterType = {
    times: [
      {
        Key: "1分钟",
        Value: 1,
      },
      {
        Key: "2分钟",
        Value: 2,
      },
      {
        Key: "3分钟",
        Value: 3,
      },
      {
        Key: "4分钟",
        Value: 4,
      },
      {
        Key: "5分钟",
        Value: 5,
      },
      {
        Key: "10分钟",
        Value: 10,
      },
      {
        Key: "15分钟",
        Value: 15,
      },
      {
        Key: "20分钟",
        Value: 20,
      },
      {
        Key: "25分钟",
        Value: 25,
      },
      {
        Key: "30分钟",
        Value: 30,
      },
      {
        Key: "45分钟",
        Value: 45,
      },
      {
        Key: "60分钟",
        Value: 60,
      },
    ],
    count: [
      {
        Key: "1次",
        Value: 1,
      },
      {
        Key: "2次",
        Value: 2,
      },
      {
        Key: "3次",
        Value: 3,
      },
      {
        Key: "4次",
        Value: 4,
      },
      {
        Key: "5次",
        Value: 5,
      },
    ],
    trainType: [
      {
        Key: "自动",
        Value: 1,
      },
      {
        Key: "手动",
        Value: 2,
      },
    ],
    level: levelOptionValue,
    f: fOptionValue,
    pload: ploadOptionValue,
    difficult: difficultOptionValue,
  };
  if (MFType === 2) {
    obj.times = timesOptionValue;
    obj.count = countOptionValue;
  }
  return obj;
};
