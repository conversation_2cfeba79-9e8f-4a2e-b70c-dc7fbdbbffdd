import Content_Api from "@/api/content";
import {
  type PageQueryConsumableInputDTO,
  type GetInstrumentInputDTO,
  type PageQueryActionUnitParams,
  type MoItemAliasInputDTO,
} from "@/api/content/types";
import Dictionary_Api from "@/api/dictionary";
import Passport_Api from "@/api/passport";
import { type OrganizationListInputDTO } from "@/api/passport/types";
import { type EpPropMergeTypeWithNull } from "element-plus";

// 类型定义
export interface DictQueryRule {
  Field: string;
  Value: string | boolean;
  Operate: number;
}

export interface DictQueryParams {
  PageCondition: {
    PageIndex: number;
    PageSize: number;
    SortConditions: {
      SortField: string;
      ListSortDirection: number;
    }[];
  };
  FilterGroup: {
    Rules: DictQueryRule[];
    Groups: {
      Rules: DictQueryRule[];
      Operate: number;
    }[];
    Operate: number;
  };
}

export interface RecoveryParams {
  PageSize: number;
  Key: string;
  IsEnabled: EpPropMergeTypeWithNull<boolean>;
  IsPublish?: EpPropMergeTypeWithNull<boolean>;
  Rules?: DictQueryRule[];
  SortConditions?: {
    SortField: string;
    ListSortDirection: number;
  }[];
}

// 获取此条字典数据通用）
export const getDictListData = async (code: string, params: RecoveryParams) => {
  try {
    const { Data } = await Dictionary_Api.getDictByCode(code);
    const queryParams = createDefaultDictQueryParams(
      Data.Id as string,
      params.PageSize,
      params.Key,
      params.IsEnabled,
      params.IsPublish || true,
      params.SortConditions || [{ SortField: "CustomSort", ListSortDirection: 1 }]
    );
    const res = await Dictionary_Api.readDict(queryParams);
    return res.Data.Total ? res.Data.Rows : [];
  } catch (err) {
    return handleDictApiError(err, code);
  }
};
// 工具函数
const createDefaultDictQueryParams = (
  dictId: string,
  pageSize: number,
  key: string = "",
  isEnabled: boolean = true,
  isPublish: boolean = true,
  SortConditions: {
    SortField: string;
    ListSortDirection: number;
  }[] = [{ SortField: "CustomSort", ListSortDirection: 1 }]
): DictQueryParams => ({
  PageCondition: {
    PageIndex: 1,
    PageSize: pageSize,
    SortConditions,
  },
  FilterGroup: {
    Rules: [
      { Field: "DictId", Value: dictId, Operate: 3 },
      { Field: "IsEnabled", Value: isEnabled, Operate: 3 },
      { Field: "IsPublish", Value: isPublish, Operate: 3 },
    ],
    Groups: [
      {
        Rules: [
          { Field: "Key", Value: key, Operate: 11 },
          { Field: "Value", Value: key, Operate: 11 },
          { Field: "PinyinCode", Value: key, Operate: 11 },
        ],
        Operate: 2,
      },
    ],
    Operate: 1,
  },
});

const handleDictApiError = (error: unknown, functionName: string): never => {
  console.error(`${functionName}Error:`, error);
  throw new Error(`Failed to fetch dictionary data: ${functionName}`);
};

// API 函数
export const getRecoveryTypeList = async (params: RecoveryParams): Promise<ReadDict[]> => {
  try {
    const { Data } = await Dictionary_Api.getDictByCode("RecoveryClassify");
    const queryParams = createDefaultDictQueryParams(
      Data.Id as string,
      params.PageSize,
      params.Key,
      params.IsEnabled
    );
    const res = await Dictionary_Api.readDict(queryParams);
    return res.Data.Total ? res.Data.Rows : [];
  } catch (err) {
    return handleDictApiError(err, "getRecoveryTypeList");
  }
};

export const getMoItemTypeList = async (): Promise<MoItemTypeList[]> => {
  try {
    const res = await Content_Api.getMoItemType();
    return res.Type === 200 && res.Data.length ? res.Data : [];
  } catch (err) {
    return handleDictApiError(err, "getMoItemTypeList");
  }
};

export const getMoItemGroupList = async (): Promise<string[]> => {
  try {
    const res = await Content_Api.getMoItemTags();
    return res.Type === 200 && res.Data.length ? res.Data : [];
  } catch (err) {
    return handleDictApiError(err, "getMoItemGroupList");
  }
};

export const getActionUnitList = async (
  params: PageQueryActionUnitParams
): Promise<ActionUnit[]> => {
  try {
    const res = await Content_Api.pageQueryActionUnit(params);
    return res.Type === 200 && res.Data.TotalCount ? res.Data.Data : [];
  } catch (err) {
    return handleDictApiError(err, "getActionUnitList");
  }
};

export const getDiseaseList = async (orgId?: string): Promise<ReadDict[]> => {
  return getDictionaryList("DiseaseDict", orgId);
};
/** 获取字典数据列表 */
export const getDictionaryList = async (code: string, orgId?: string): Promise<ReadDict[]> => {
  try {
    const res = await Dictionary_Api.getDict({ code, orgId: orgId || "" });
    return res.Type === 200 && res.Data.length ? res.Data : [];
  } catch (err) {
    return handleDictApiError(err, code);
  }
};

/** 获取平台机构 */
export const getBaseOrganizationList = async (
  params?: OrganizationListInputDTO
): Promise<BaseOrganization[]> => {
  const defaultParams: OrganizationListInputDTO = {
    ...{
      PageIndex: 1,
      PageSize: 1000,
      IsEnabled: true,
      Pageable: true,
      Scopeable: true,
      DtoTypeName: "QueryOrgDtoForDropDownList",
    },
    ...params,
  };
  try {
    const res = await Passport_Api.getOrganizationList(defaultParams);
    return res.Type === 200 && res.Data.Rows ? res.Data.Rows : [];
  } catch (err) {
    return handleDictApiError(err, "getOrganizationList");
  }
};
/** 获取平台某个机构的科室 */
export const getDeptList = async (params: { OrgId: string }) => {
  if (!params.OrgId) {
    return [];
  }
  let defaultParams = {
    IsEnabled: true,
    Keyword: "",
    Ids: [],
    DeptTypes: [],
    Pageable: false,
    ...params,
  };
  const queryParams = {
    Pageable: defaultParams.Pageable,
    DtoTypeName: "DepartmentOutputDto1",
    Keyword: defaultParams.Keyword,
    IsEnabled: defaultParams.IsEnabled,
    Ids: defaultParams.Ids,
    OrgId: defaultParams.OrgId,
    DeptTypes: defaultParams.DeptTypes,
    IsLocked: false,
    SingleOne: false,
  };
  const res = await Passport_Api.getDeptReadList(queryParams);
  return res.Type === 200 && res.Data ? res.Data : [];
};

/** 获取平台的科别 */
export const getDeptDivisionsList = async (params = {}): Promise<ReadDict[]> => {
  try {
    const defaultParams = {
      PageIndex: 1,
      PageSize: 200,
      IsEnabled: true,
      IsPublish: true,
      ...params,
    };

    const queryParams = createDefaultDictQueryParams(
      "5",
      defaultParams.PageSize,
      "",
      defaultParams.IsEnabled
    );

    const res = await Dictionary_Api.readDict(queryParams);
    return res.Type === 200 && res.Data.Total ? res.Data.Rows : [];
  } catch (err) {
    return handleDictApiError(err, "getDeptDivisionsList");
  }
};

export const getWesternDiagnosisList = async (params: RecoveryParams): Promise<ReadDict[]> => {
  try {
    const { Data } = await Dictionary_Api.getDictByCode("IcdDict");
    const queryParams = createDefaultDictQueryParams(
      Data.Id as string,
      params.PageSize,
      params.Key,
      params.IsEnabled
    );
    const res = await Dictionary_Api.readDict(queryParams);
    return res.Data.Total ? res.Data.Rows : [];
  } catch (err) {
    return handleDictApiError(err, "getWesternDiagnosisList");
  }
};

export const getCMDiseasesDictList = async (params: RecoveryParams): Promise<ReadDict[]> => {
  try {
    const { Data } = await Dictionary_Api.getDictByCode("CMDiseasesDict");
    const queryParams = createDefaultDictQueryParams(
      Data.Id as string,
      params.PageSize,
      params.Key,
      params.IsEnabled
    );
    const res = await Dictionary_Api.readDict(queryParams);
    return res.Data.Total ? res.Data.Rows : [];
  } catch (err) {
    return handleDictApiError(err, "getCMDiseasesDictList");
  }
};

export const getCMDiseasesSymptomList = async (params: RecoveryParams): Promise<ReadDict[]> => {
  try {
    const { Data } = await Dictionary_Api.getDictByCode("CMSymptomsDict");
    const queryParams = createDefaultDictQueryParams(
      Data.Id as string,
      params.PageSize,
      params.Key,
      params.IsEnabled
    );

    // 如果有自定义规则，替换默认的搜索规则
    if (params.Rules) {
      queryParams.FilterGroup.Groups[0].Rules = params.Rules;
    }

    const res = await Dictionary_Api.readDict(queryParams);
    return res.Data.Total ? res.Data.Rows : [];
  } catch (err) {
    return handleDictApiError(err, "getCMDiseasesSymptomList");
  }
};

/** 获取设备类型 */
export const getInstrumentTypeList = async (
  params: GetInstrumentInputDTO
): Promise<BaseInstrument[]> => {
  const defaultParams: GetInstrumentInputDTO = {
    page: params.page ?? 1,
    pageSize: params.pageSize ?? 9000,
    isEnable: params.isEnable ?? true,
  };
  const res = await Content_Api.getInstrumentsPageData(defaultParams);
  if (res.Type === 200) {
    return res.Data.Data;
  }
  return [];
};

/** 获取耗材类型 */
export const getConsumablesTypeList = async (
  params: PageQueryConsumableInputDTO
): Promise<BaseConsumables[]> => {
  const defaultParams: PageQueryConsumableInputDTO = {
    page: params.page ?? 1,
    pageSize: params.pageSize ?? 9000,
    isEnable: params.isEnable ?? true,
  };
  const res = await Content_Api.getConsumablesPageData(defaultParams);
  if (res.Type === 200) {
    return res.Data.Data;
  }
  return [];
};
/** 获取部位 */
export const getBodyPartList = async (params: RecoveryParams): Promise<ReadDict[]> => {
  try {
    const { Data } = await Dictionary_Api.getDictByCode("PartDict");
    const queryParams = createDefaultDictQueryParams(
      Data.Id as string,
      params.PageSize,
      params.Key,
      params.IsEnabled,
      params.IsPublish
    );
    const res = await Dictionary_Api.readDict(queryParams);
    return res.Data.Total ? res.Data.Rows : [];
  } catch (err) {
    return handleDictApiError(err, "getBodyPartList");
  }
};
/** 获取医嘱简称 */
export const getMoItemAliasNameList = async (params: MoItemAliasInputDTO): Promise<string[]> => {
  const defaultParams: MoItemAliasInputDTO = {
    ...{
      DeptId: null,
      OrgId: null,
      FilterOrg: true,
      IsEnable: true,
    },
    ...params,
  };
  const res = await Content_Api.getMoItemAliasName(defaultParams);
  return res.Type === 200 && res.Data.length ? res.Data : [];
};
/** 获取功能障碍类型 */
export const getDysfunctionTypeList = async (params: RecoveryParams): Promise<ReadDict[]> => {
  try {
    const { Data } = await Dictionary_Api.getDictByCode("DysfunctionTypeDict");
    const queryParams = createDefaultDictQueryParams(
      Data.Id as string,
      params.PageSize,
      params.Key,
      params.IsEnabled,
      params.IsPublish,
      [{ SortField: "Key", ListSortDirection: 1 }]
    );
    const res = await Dictionary_Api.readDict1(queryParams);
    return res.Data.Total ? res.Data.Rows : [];
  } catch (err) {
    return handleDictApiError(err, "getDysfunctionTypeList");
  }
};
/** 获取功能障碍列表 */
export const getDysfunctionList = async (params: RecoveryParams): Promise<ReadDict[]> => {
  try {
    const { Data } = await Dictionary_Api.getDictByCode("DysfunctionDict");
    const queryParams = createDefaultDictQueryParams(
      Data.Id as string,
      params.PageSize,
      params.Key,
      params.IsEnabled,
      params.IsPublish
    );
    const res = await Dictionary_Api.readDict1(queryParams);
    return res.Data.Total ? res.Data.Rows : [];
  } catch (err) {
    return handleDictApiError(err, "getDysfunctionList");
  }
};
/** 获取病种类型 */
export const getDiseaseTypeList = async (params: RecoveryParams): Promise<ReadDict[]> => {
  try {
    const { Data } = await Dictionary_Api.getDictByCode("DiseaseTypeDict");
    const queryParams = createDefaultDictQueryParams(
      Data.Id as string,
      params.PageSize,
      params.Key,
      params.IsEnabled,
      params.IsPublish,
      [{ SortField: "Key", ListSortDirection: 1 }]
    );
    const res = await Dictionary_Api.readDict(queryParams);
    return res.Data.Total ? res.Data.Rows : [];
  } catch (err) {
    return handleDictApiError(err, "getDiseaseTypeList");
  }
};
/* 获取icd数据 */
export const getIcdList = async (params: RecoveryParams): Promise<ReadDict[]> => {
  try {
    const { Data } = await Dictionary_Api.getDictByCode("IcdDict");
    const queryParams = createDefaultDictQueryParams(
      Data.Id as string,
      params.PageSize,
      params.Key,
      params.IsEnabled,
      params.IsPublish,
      [{ SortField: "Key", ListSortDirection: 1 }]
    );
    const res = await Dictionary_Api.readDict(queryParams);
    return res.Data.Total ? res.Data.Rows : [];
  } catch (err) {
    return handleDictApiError(err, "getIcdList");
  }
};
