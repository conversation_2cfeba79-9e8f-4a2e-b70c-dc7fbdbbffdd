<template>
  <div class="app-container">
    <div class="h-full flex flex-col p-10px pb-0" style="background-color: var(--el-bg-color)">
      <div class="ml-auto mb-10px">
        <el-button type="primary" @click="handleClick('add')">添加机构</el-button>
      </div>
      <div v-loading="listLoading" class="flex-1 overflow-auto">
        <div
          class="code-container flex flex-wrap gap-10px row-gap-10px"
          :style="{ '--columns': columns }"
        >
          <el-card v-for="(item, index) in items" :key="index" class="code-item">
            <div class="flex">
              <div class="w-200px h-200px flex-shrink-0">
                <QRCode
                  v-if="item._qrCode"
                  :value="item._qrCode"
                  :size="200"
                  :need-upload="false"
                />
                <div
                  v-else
                  class="flex items-center justify-center h-full border-1 border-gray-200"
                >
                  暂无二维码
                </div>
              </div>
              <div class="ml-10px">
                <p>{{ item.InviterName }}</p>
                <p>跳转医院：{{ item.OrganizationName }}</p>
                <p>备注： {{ item.Remark }}</p>
              </div>
            </div>
            <div
              class="text-center mt-10px cursor-pointer h-30px line-height-30px font-bold"
              style="color: var(--el-color-primary)"
              @click="handleClick('edit', item)"
            >
              编辑
            </div>
          </el-card>
        </div>
      </div>
      <Pagination
        v-model:total="totalCount"
        v-model:page="pageIndex"
        v-model:limit="pageSize"
        class="pt-10px"
        @pagination="loadData"
      />
    </div>
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      align-center
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      @closed="modifyValue = null"
    >
      <GeneralizationCodeModify
        ref="modify"
        :disabled="submitting"
        :value="modifyValue"
        @cancel="dialogVisible = false"
        @submit="submit"
      />
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import Passport_API from "@/api/passport";
import { type InviterAddOrUpdateInput, type InviterDTO } from "@/api/passport/inviter";
import GeneralizationCodeModify from "./views/GeneralizationCodeModify.vue";

export type InviterItem = InviterDTO & { _qrCode: string };

const items = ref<InviterItem[]>([]);
const totalCount = ref(0);
const pageIndex = ref(1);
const pageSize = ref(10);
const listLoading = ref(false);
const columns = ref(4);

const updateColumns = () => {
  const container = document.querySelector(".code-container");
  if (container) {
    const containerWidth = container.clientWidth;
    const minWidth = 400; // 每个卡片的最小宽度
    const gap = 10; // 卡片间距
    const c = Math.floor((containerWidth + gap) / (minWidth + gap));
    columns.value = c;
  }
};
const loadData = async () => {
  listLoading.value = true;
  const res = await Passport_API.getInviterList({
    PageIndex: pageIndex.value,
    PageSize: pageSize.value,
  });
  listLoading.value = false;
  if (res.Type != 200) {
    ElMessage.error(res.Content ?? "加载数据失败");
    return;
  }

  items.value = res.Data.Data.map((v) => {
    return {
      ...v,
      _qrCode: `https://oss-biz.kangfx.com?orgId=${v.OrganizationId}&genCode=${v.Id}&orgName=${v.OrganizationName}`,
    };
  });
  totalCount.value = res.Data.TotalCount;
};

onMounted(() => {
  loadData();
  updateColumns();
  window.addEventListener("resize", updateColumns);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", updateColumns);
});

const handleSizeChange = (e: number) => {
  pageSize.value = e;
  loadData();
};
const handleCurrentChange = (e: number) => {
  pageIndex.value = e;
  loadData();
};

const dialogVisible = ref(false);
const dialogTitle = ref("");
const submitting = ref(false);
const modifyValue = ref<InviterAddOrUpdateInput | null>(null);

const handleClick = (oper: "add" | "edit", row?: InviterItem) => {
  switch (oper) {
    case "add":
      dialogTitle.value = "添加";
      modifyValue.value = {
        InviterName: "",
        OrganizationId: "",
        OrganizationName: null,
      };
      break;
    case "edit":
      dialogTitle.value = "编辑";
      modifyValue.value = JSON.parse(JSON.stringify(row!)) as InviterItem;
      break;
  }
  dialogVisible.value = true;
};

const submit = async (value: InviterAddOrUpdateInput) => {
  if (value) {
    if (value.Id) {
      submitting.value = true;
      const res = await Passport_API.addOrUpdateInviter(value, "Update");
      submitting.value = false;
      if (res.Type != 200) {
        ElMessage.error(res.Content ?? "操作失败");
        return;
      }
      ElNotification.success(res.Content ?? "操作成功");
    } else {
      submitting.value = true;
      const res = await Passport_API.addOrUpdateInviter(value, "Add");
      submitting.value = false;
      if (res.Type != 200) {
        ElMessage.error(res.Content ?? "操作失败");
        return;
      }
      ElNotification.success(res.Content ?? "操作成功");
    }
    dialogVisible.value = false;
    loadData();
  }
};
</script>
<style lang="scss" scoped>
.code-container {
  // --columns: 4; // 通过变量赋值
  --item-width: calc((100% - 10px - (var(--columns) - 1) * 10px) / var(--columns));
  min-width: 410px;
  .code-item {
    width: var(--item-width);
    box-sizing: border-box;
    min-width: 400px;
  }
}
</style>
