FROM registry-vpc.cn-chengdu.aliyuncs.com/kangfx-library/alpine:3.21 as build-stage
ARG BRANCH_NAME

WORKDIR /app
COPY . .
# RUN cnpm install --save-dev cache-loader@4.1.0 webpack@3.11.0 webpack-cli@5.0.1 html-webpack-plugin@5.5.0 webpack-dev-server@4.11.1 webpack-dev-middleware@6.0.1
# RUN cnpm install --save regenerator-runtime@0.13.11 lodash@4.17.21 resize-observer-polyfill@1.5.1
RUN node --version
RUN npm --version
RUN npm config set registry https://registry.npmmirror.com/
RUN npm config get registry
RUN npm config set fetch-retry-mintimeout 20000
RUN npm config set fetch-retry-maxtimeout 120000
RUN npm install pnpm -g
RUN pnpm install
RUN pnpm run build:$BRANCH_NAME

FROM registry-vpc.cn-chengdu.aliyuncs.com/kangfx-library/nginx:1.28.0-alpine3.21
HEALTHCHECK NONE
USER nginx
COPY nginx/default.conf /etc/nginx/conf.d/default.conf
COPY --from=build-stage /app/dist /usr/share/nginx/html