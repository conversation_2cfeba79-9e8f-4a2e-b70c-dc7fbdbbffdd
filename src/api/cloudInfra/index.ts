import request from "@/utils/request";

const CLOUD_INFRA_SMS_PATH = "/cloudInfra/sms";

/**
 * 获取验证码
 * @param {Object} params 参数
 * @param {string} params.phoneNumber 手机号
 * @param {'Signin' | 'Register' | 'UpdatePwd' | 'UpdatePhone' | 'CAPasswordEdit'} params.templateType 模板类型
 * @param {string} params.token 认证 token
 * @returns sessionId
 */
export const sendCaptcha = (params: {
  phoneNumber: string;
  templateType: "Signin" | "Register" | "UpdatePwd" | "UpdatePhone" | "CAPasswordEdit";
  token: string;
}) => {
  return request.post<string>(
    `${CLOUD_INFRA_SMS_PATH}/sendCaptcha`,
    {
      PhoneNumber: params.phoneNumber,
      TemplateType: params.templateType,
    },
    { headers: { Authorization: params.token } }
  );
};
