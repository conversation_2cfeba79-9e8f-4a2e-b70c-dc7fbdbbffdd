import request from "@/utils/request";

const BFF_BIZ_PATH = "/bff/biz";
const BFF_CONFIG_PATH = "/bff/config";

const Bff_Api = {
  /** 获取mongoDb里面的正则验证 */
  getSettingWithoutAuth(data: any, token: string) {
    return request.post<{ Data: DynamicConfig }[]>(
      `${BFF_CONFIG_PATH}/getSettingWithoutAuth`,
      data,
      {
        headers: {
          authorization: token,
        },
      }
    );
  },

  // ---------------- Bff_Biz ----------------
  getByCode(params: { code: string }): Promise<ServerResult<GetCodeItem[]>> {
    return request.get(`${BFF_BIZ_PATH}/GetByCode`, { params });
  },
  /**
   * 获取基础配置
   * @param {*} params
   * @returns
   */
  getSettingList(params: any): Promise<ServerResult<any[]>> {
    return request.post(`${BFF_BIZ_PATH}/getList`, params);
  },
  /**
   * 获取基础配置
   * @param {*} params
   * @returns
   */
  modifySetting(params: any): Promise<ServerResult<any>> {
    return request.post(`${BFF_BIZ_PATH}/modify`, params);
  },
};

export default Bff_Api;
