import { type EpPropMergeTypeWithNull } from "element-plus";
import {
  type <PERSON><PERSON>Token,
  type UserProfileParams,
  type UserEventInputDTO,
  type UserEventItem,
  type InsertUserEventDTO,
  type BusinessGoalPageParams,
  type BusinessGoalInputDTO,
  type OrganizationAuthentication,
  type UpsertAuthenticationInputDTO,
  type DepartmentInputDTO,
  type ReadInputDTO,
  type OrganizationConsortiumListInputDTO,
  type UpdatePartParams,
  type CreateUserInputDTO,
  type GetAuthDoctorsParams,
  type GetAuthUsersInputDTO,
  type PatientCertificationItem,
  type AuthDoctorInfo,
  type DoctorCertification,
  type DoctorAuthenticationParams,
  type AllOrganizationListInputDTO,
  type PageOrganizationListInputDTO,
} from "./types";
import request from "@/utils/request";
import CryptoJS from "crypto-js";

// --------------- passport_connect
const Passport_Connect = "/passport/connect";
// --------------- passport_user
const Passport_User = "/passport/api/user";
// --------------- passport_organization
const Passport_Organization = "/passport/api/organization";
// --------------- passport_identity
const Passport_Identity = "/passport/api/identity";
// --------------- passport_department
const Passport_Department = "/passport/api/department";
// --------------- passport_role
const Passport_Role = "/passport/api/Role";
// --------------- passport_consortium
const Passport_Consortium = "/passport/consortium";
// --------------- passport_organization_consortium
const Passport_Organization_Consortium = "/passport/OrganizationConsortium";
// --------------- passport_user_organization
const Passport_UserOrganization = "/passport/UserOrganization";

const aesKey = "3ajIVAjObyxA0KpR";
const aesIV = "i5W14eTmouZ5Aj2P";

import * as identity from "./identity";
import * as inviter from "./inviter";

const Passport_Api = {
  ...identity,
  ...inviter,
  // --------------- connect ---------------

  // 获取用户登录 token 相关的数据
  getUserToken(username: string, password: string): Promise<ServerResult<LoginToken>> {
    const str = encodeURI(
      `${import.meta.env.VITE_APP_CLIENT_ID}:${import.meta.env.VITE_APP_CLIENT_SECRET}`
    );
    const authorization = btoa(str);

    const encrypted = CryptoJS.AES.encrypt(
      JSON.stringify({ username, password }),
      CryptoJS.enc.Utf8.parse(aesKey),
      {
        iv: CryptoJS.enc.Utf8.parse(aesIV),
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      }
    ).toString(CryptoJS.format.Hex);
    // console.log("encrypted", encrypted);

    const data = new URLSearchParams();
    data.append("grant_type", "aes_password");
    data.append("secret", encrypted);
    return request.post(`${Passport_Connect}/token`, data, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: "Basic " + authorization,
      },
    });
  },

  /**
   * 获取端token
   */
  generateToken(data: { appId: string; appSecret: string }) {
    return request.post<{ token_type: string; access_token: string }>(
      `/passport/token/generate`,
      data
    );
  },

  // --------------- user ---------------
  // 通过 token 获取登录人的信息
  getUserProfileGetMethod(): Promise<ServerResult<BaseUserProfile>> {
    return request.get(`${Passport_User}/GetUserProfile`);
  },
  /**
   * 获取用户列表数据
   */
  getUserProfile(data: UserProfileParams): Promise<ServerResult<ListRowTotal<BaseUserProfile>>> {
    return request.post(`${Passport_User}/GetUserProfile`, data);
  },
  /** 解绑医助 */
  delUserClaims(
    data: {
      ClaimType: string;
      OnlyOne: boolean;
      UserId: string;
    }[]
  ): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/DelUserClaims`, data);
  },
  /** 修改人员部分信息 */
  updatePart(data: UpdatePartParams[]): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/UpdatePart`, data);
  },
  /** 其他的绑定 */
  setUserClaims(
    data: {
      ClaimType: string;
      ClaimValue: string;
      OnlyOne: boolean;
      UserId: string;
      Remark: string;
    }[]
  ): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/SetUserClaims`, data);
  },
  /** 医生和医助进行绑定 */
  setAssistants(
    data: {
      AssistantId: EpPropMergeTypeWithNull<string>;
      DoctorId: string;
    }[]
  ): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/SetAssistants`, data);
  },
  /** 获取用户的跟进记录或者突发事件 */
  getUserEvent(params: UserEventInputDTO): Promise<ServerResult<ListRowsTotal<UserEventItem>>> {
    return request.get(`${Passport_User}/getUserEvent`, { params });
  },
  /** 新增跟进记录或者突发事件 */
  insertUserEvent(data: InsertUserEventDTO[]): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/InsertUserEvent`, data);
  },
  /** 编辑跟进记录或者突发事件 */
  updateUserEvent(data: InsertUserEventDTO[]): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/UpdateUserEvent`, data);
  },
  /** 删除跟进记录或者突发事件 */
  deleteUserEvent(data: { Ids: string[] }): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/DeleteUserEvent`, data);
  },
  /** 获取用户认证信息 */
  getDoctorOrganizationAuthentications(params: {
    userId: string;
  }): Promise<ServerResult<OrganizationAuthentication[]>> {
    return request.get(`${Passport_User}/GetDoctorOrganizationAuthentications`, { params });
  },
  /** 更新用户认证信息 */
  upsertAuthenticationData(data: UpsertAuthenticationInputDTO[]): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/UpsertAuthenticationData`, data);
  },

  /** 通过用户id获取用户信息 */
  getUserById(params: { id: string }): Promise<ServerResult<UserOutputDTO>> {
    return request.get(`${Passport_User}/GetUserById`, { params });
  },
  /** 设置用户角色 */
  setRoles(data: { UserId: string; RoleIds: string[] }): Promise<ServerResult<number>> {
    return request.post(`${Passport_User}/SetRoles`, data);
  },

  /** 创建用户 */
  createUser(data: CreateUserInputDTO[]): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/Create`, data);
  },
  /** 全量更新用户 */
  updateUser(data: CreateUserInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/Update`, data);
  },
  /** 局部更新用户 */
  updatePutPart(data: [CreateUserInputDTO]): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/UpdatePart`, data);
  },
  /** 修改用户身份证信息 */
  updateUserCertificates(data: UserCertificate[]): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/UpdateUserCertificates`, data);
  },

  /** 获取医生认证审核列表 */
  getAuthDoctors(
    params: GetAuthDoctorsParams
  ): Promise<ServerResult<ListRowsTotal<DoctorCertification>>> {
    return request.post(`${Passport_User}/GetAuthDoctors`, params);
  },
  /** 患者认证审核列表 */
  getAuthUsers(
    params: GetAuthUsersInputDTO
  ): Promise<ServerResult<ListRowsTotal<PatientCertificationItem>>> {
    return request.post(`${Passport_User}/GetAuthUsers`, params);
  },

  /** 获取单个医生认证审核信息 */
  getSingleAuthDoctor(workflowId: string): Promise<ServerResult<AuthDoctorInfo>> {
    return request.get(`${Passport_User}/GetSingleAuthDoctor/${workflowId}`, {});
  },

  /** 一键审核机构待审核的数据 */
  doctorAuthenticationOperationOneKey(
    data: DoctorAuthenticationParams
  ): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/DoctorAuthenticationOperationOneKey`, data);
  },

  /** 提交医生认证数据 */
  doctorAuthenticationOperation(data: DoctorAuthenticationParams): Promise<ServerResult<null>> {
    return request.post(`${Passport_User}/DoctorAuthenticationOperation`, data);
  },

  // --------------- organization ---------------

  /**
   * 获取医院列表
   */
  getOrganizationList(
    data: PageOrganizationListInputDTO
  ): Promise<ServerResult<ListRowsTotal<BaseOrganization>>> {
    return request.post(`${Passport_Organization}/GetList`, {
      ...data,
      Pageable: true,
      SingleOne: false,
    });
  },

  /**
   * 获取医院列表
   * 不分页，获取全量数据
   */
  getAllOrganizationList(
    data: AllOrganizationListInputDTO
  ): Promise<ServerResult<BaseOrganization[]>> {
    return request.post<BaseOrganization[]>(`${Passport_Organization}/GetList`, {
      ...data,
      Pageable: false,
      SingleOne: false,
    });
  },

  /** 获取医院详情 */
  getOrganizationById(params: { id: string }): Promise<ServerResult<BaseOrganization>> {
    return request.get(`${Passport_Organization}/GetOrganizationById`, { params });
  },
  /** 修改医院信息 */
  updateOrganization(data: BaseOrganization[]): Promise<ServerResult<null>> {
    return request.post(`${Passport_Organization}/Update`, data);
  },
  /** 创建医院 */
  createOrganization(data: BaseOrganization[]): Promise<ServerResult<null>> {
    return request.post(`${Passport_Organization}/Create`, data);
  },
  /** 通过区域codes获取医院列表 */
  getListByRegion(data: {
    PageIndex: number;
    PageSize: number;
    RegionCodes: string[];
    IsEnabled?: boolean;
  }): Promise<ServerResult<ListRowsTotal<BaseOrganization>>> {
    return request.post(`${Passport_Organization}/GetListByRegion`, data);
  },
  /** 获取运营目标数据 */
  getBusinessGoalPage(data: { Month: string }): Promise<ServerResult<BusinessGoalPageParams[]>> {
    return request.post(`${Passport_Organization}/GetBusinessGoalPage`, data);
  },
  /** 新增运营目标数据 */
  insertBusinessGoal(data: BusinessGoalInputDTO[]): Promise<ServerResult<null>> {
    return request.post(`${Passport_Organization}/InsertBusinessGoal`, data);
  },
  /** 编辑运营目标数据 */
  updateBusinessGoal(data: BusinessGoalInputDTO[]): Promise<ServerResult<null>> {
    return request.post(`${Passport_Organization}/UpdateBusinessGoal`, data);
  },

  // --------------- Identity ---------------

  /** 解锁 */
  checkPassword(params: { userId: string; password: string }): Promise<ServerResult<boolean>> {
    return request.get(`${Passport_Identity}/CheckPassword`, {
      params,
    });
  },

  // --------------- department ---------------

  /** 获取全部科室列表 */
  getDeptReadList(data: DepartmentInputDTO): Promise<ServerResult<BaseDepartment[]>> {
    return request.post(`${Passport_Department}/GetList`, {
      ...data,
      Pageable: false,
      SingleOne: false,
    });
  },

  // --------------- role ---------------

  /** 获取角色列表 */
  read(params: ReadInputDTO): Promise<ServerResult<BaseRole[]>> {
    return request.get(`${Passport_Role}/Read`, { params });
  },

  // --------------- consortium ---------------

  /** 获取医联体列表 */
  getConsortiumList(): Promise<ServerResult<Consortium[]>> {
    return request.post(`${Passport_Consortium}/GetList`, {});
  },

  /** 删除医联体 */
  deleteConsortium(ids: string[]): Promise<ServerResult<number>> {
    return request.post(`${Passport_Consortium}/Delete`, ids);
  },

  /** 添加医联体 */
  addConsortium(data: Consortium[]): Promise<ServerResult<Consortium[]>> {
    return request.post(`${Passport_Consortium}/Add`, data);
  },

  /** 编辑医联体 */
  updateConsortium(data: Consortium[]): Promise<ServerResult<number>> {
    return request.post(`${Passport_Consortium}/Update`, data);
  },

  // --------------- organization_consortium ---------------

  /** 获取医联体下机构列表 */
  getOrganizationConsortiumList(
    params: OrganizationConsortiumListInputDTO
  ): Promise<ServerResult<ListDataTotalCount<OrganizationConsortium>>> {
    return request.post(`${Passport_Organization_Consortium}/Get`, params);
  },

  /** 给医联体添加机构 */
  addOrganizationConsortiums(
    params: OrganizationConsortium[]
  ): Promise<ServerResult<OrganizationConsortium[]>> {
    return request.post(`${Passport_Organization_Consortium}/Add`, params);
  },

  /** 删除医联体中机构 */
  deleteOrganizationConsortium(ids: string[]): Promise<ServerResult<number>> {
    return request.post(`${Passport_Organization_Consortium}/Delete`, ids);
  },

  // --------------- user_organization ---------------

  /** 获取用户机构列表 */
  updateUserOrganization(data: OrganizationAuthentication[]): Promise<ServerResult<null>> {
    return request.post(`${Passport_UserOrganization}/Update`, data);
  },
};
export default Passport_Api;
