import { type EpPropMergeTypeWithNull } from "element-plus";

/** 登录表单数据 */
export interface LoginFormData {
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
  /** 登录模式 */
  grant_type: string;
}
export type LoginToken = {
  access_token: string;
  expires_in: number;
  refresh_token: string;
  scope: string;
  token_type: string;
};

export type OrganizationListParams = {
  DtoTypeName: string;
  IsEnabled: boolean;
  PageIndex: number;
  PageSize: number;
  IsTreatment?: boolean;
  Pageable: boolean;
  Scopeable?: boolean;
};

export type UserProfileParams = {
  Keyword: string;
  RoleTypes?: string[]; // 数组类型
  RoleType?: EpPropMergeTypeWithNull<string>;
  OrgIds?: EpPropMergeTypeWithNull<string[]>; // 允许为 null
  IsEnabled: EpPropMergeTypeWithNull<boolean>;
  Pageable?: boolean;
  SingleOne?: boolean;
  Scopeable?: boolean;
  PageIndex: number;
  PageSize: number;
  DeptIds?: EpPropMergeTypeWithNull<string[]>; // 允许为 null
  DtoTypeName?: string;
  HasElectronicPractice?: EpPropMergeTypeWithNull<boolean>;
  HasWeChatQrCode?: EpPropMergeTypeWithNull<boolean>;
  Ids?: string[];
  HasTherapistQualifyImg?: EpPropMergeTypeWithNull<boolean>;
  HasNurseQualifyImg?: EpPropMergeTypeWithNull<boolean>;
  HasDoctorQualifyImg?: EpPropMergeTypeWithNull<boolean>;
};

export interface UpdatePartParams {
  // 主要执业机构
  PracticeOrganizationName?: string;
  // 资料是否完善
  ProfileDescription?: string;
  OnlyFillNotNull: boolean;
  UserId: string;
}

export type OrganizationListInputDTO = {
  IsEnabled?: EpPropMergeTypeWithNull<boolean>;
  Keyword?: string;
  DtoTypeName?: string;
  PageIndex?: number;
  PageSize?: number;
  Pageable?: boolean;
  Scopeable?: boolean;
  SingleOne?: boolean;
  IsTreatment?: EpPropMergeTypeWithNull<boolean>;
  OrgFrontendAssistantId?: EpPropMergeTypeWithNull<string>;
  OrgBackendAssistantId?: EpPropMergeTypeWithNull<string>;
  Ids?: EpPropMergeTypeWithNull<string[]>;
};

export type AllOrganizationListInputDTO = Omit<
  OrganizationListInputDTO,
  "Pageable" | "PageIndex" | "PageSize" | "SingleOne"
>;

export type PageOrganizationListInputDTO = Omit<OrganizationListInputDTO, "SingleOne" | "Pageable">;

export interface UserEventInputDTO {
  UserId: string;
  Type: number; // 0: 跟进记录 1: 突发事件
  PageIndex: number;
  PageSize: number;
}
export interface UserEventItem {
  Id?: string;
  UserId: string;
  Remark: string;
  EventTime: string;
  DirectorId: string;
  DirectorName: string;
  Type: number; // 0: 跟进记录 1: 突发事件
}
export interface InsertUserEventDTO {
  UserId: string; // 用户id
  Remark: string; // 备注
  EventTime: string; // 事件时间
  DirectorId: string; // 跟进人id
  DirectorName: string; // 跟进人姓名
  Type: number; // 0: 跟进记录 1: 突发事件
  Id?: string; // 编辑时需要传入
}
export interface BusinessGoalPageParams {
  AssistantId: string;
  AssistantName: string;
  Month: string;
  OperationTarget: number;
  OrgId: string;
  OrgName: string;
  Region: string;
  RegionName: string;
  TargetAmount: number;
  CreatedTime: string;
  CreatorId: string;
  UpdatedTime?: string;
  UpdaterId?: string;
  DeletedTime?: string;
  DeleterId?: string;
  Id?: EpPropMergeTypeWithNull<string>;
  IdStr?: EpPropMergeTypeWithNull<string>;
}
export interface BusinessGoalInputDTO {
  Month: string;
  Region: string;
  RegionName: string;
  OrgId: string;
  OrgName: string;
  AssistantId: string;
  AssistantName: string;
  TargetAmount: number;
  OperationTarget: number;
  Id?: EpPropMergeTypeWithNull<string>;
}
export interface OrganizationAuthentication {
  IsDefault: true;
  WorkerTitle: string;
  WorkerType: string;
  UserId: string;
  UserCertificates: UserCertificate[];
  UserDepartments: { IsMain: boolean; DeptName: string }[];
  PracticeLevel: string;
  PracticeType: string;
  PracticeRange: string;
  PracticeOrganizationName: string;
  PracticeOrganizationCode: string;
  OrganizationName: string;
  RoleNames: string[];
  State: number;
  UserWork: UserProfileUserWork;
  Skilled: string;
  SkilledTags: string;
  Abstract: string;
  AbstractTags: string;
}
export interface UpsertAuthenticationInputDTO
  extends Pick<OrganizationAuthentication, "UserCertificates" | "WorkerTitle" | "WorkerType"> {
  UserId: string;
  UpsertUserCertificateMode: number;
}
export interface DepartmentInputDTO {
  DtoTypeName: string;
  Keyword?: string;
  IsEnabled: boolean;
  Ids?: string[];
  OrgId: string; //机构Id 代替旧的 CreateOrgId
  DeptTypes?: string[];
  IsLocked: boolean;
  // Pageable: boolean; //返回结果是否分页;接口定义中直接写死为false
  // SingleOne: boolean; //接口定义中直接写死为false
}

export interface ReadInputDTO {
  Keyword?: string;
  IsEnabled?: boolean;
  SingleOne?: boolean;
}

export interface OrganizationConsortiumListInputDTO {
  ConsortiumId?: string;
  Keyword?: string;
  PageIndex: number;
  PageSize: number;
  IsEnabled?: boolean;
}

export interface CreateUserInputDTO {
  UserId?: string; // 这个是我为了方便写的 正常情况不应该有这个字段
  Id?: string;
  Name: string;
  UserName: string;
  NickName: string;
  Sex: string;
  Birthday: string;
  Code: string;
  PhoneNumber: string;
  Password?: string;
  IsEnabled: boolean;
  IsLocked: boolean;
  HeadImg: string;
  UserExternalIdentify: {
    WeChatQrCode: string;
  };
}

export type TestUserDTO = {
  UserId: string;
  Name: string;
  PhoneNumber: string;
  /** 是否统计 */
  ReportBan: boolean;
  /** 是否短信 */
  SmsBan: boolean;
  /** 是否微信 */
  WeChatBan: boolean;
  CreatedTime: string;
  CreatorId?: string;
  UpdatedTime?: string;
  UpdaterId?: string;
  DeletedTime?: string;
  DeleterId?: string;
};

export interface GetAuthDoctorsParams {
  Keyword?: string;
  State?: number;
  Type?: number;
  PageInput: PageInputDTO;
}

export interface PageInputDTO {
  PageIndex: number;
  PageSize: number;
}
export interface GetAuthUsersInputDTO {
  Keyword?: string;
  State?: string;
  PageInput: PageInputDTO;
}
export interface PatientCertificationItem {
  UserId?: string;
  Name?: string;
  Sex?: string;
  UserName?: string;
  NickName?: string;
  Birthday?: string;
  HeadImg?: string;
  PhoneNumber?: string;
  ExecutionPointers?: ExecutionPointer[];
  CreatedTime?: string;
  WorkflowId?: string;
  WorkflowStatus?: number;
  UserCertificates?: UserCertificate[];
}

export interface DoctorCertification {
  UserId?: string;
  Name?: string;
  Sex?: string;
  PhoneNumber?: string;
  ExecutionPointers?: ExecutionPointer[];
  Department?: BaseDepartment;
  Organization?: BaseOrganization;
  WorkerTitle?: string;
  CreatedTime?: string;
  WorkflowId?: string;
  WorkflowStatus?: number;
}

export interface AuthDoctorInfo {
  Id?: string;
  ReferrerId?: string;
  /** 推荐人 */
  Referrer?: any;
  PhoneNumber?: string;
  NickName?: string;
  HeadImg?: string;
  IsSystem?: boolean;
  /**
   * 职称
   * eg： 203
   */
  WorkerTitle?: string;
  /**
   * 职业类型
   * eg： nurse
   */
  WorkerType?: string;
  NowOfficeOrganizationId?: string;
  CreatedTime?: string;
  Name?: string;
  Code?: string;
  Birthday?: string;
  Sex?: string;
  IsEnabled?: boolean;
  UserNameUpdateTime?: string;
  ChangePasswordTime?: string;
  PracticeLevel?: string;
  PracticeType?: string;
  PracticeRange?: string;
  PracticeOrganizationName?: string;
  PracticeOrganizationCode?: string;
  UserWork?: UserWork;
  Organizations?: UserOrganization[];
  Departments?: UserDepartment[];
  UserClaims?: UserClaim[];
  UserCertificates?: UserCertificate[];
  UserRole?: {
    RoleIds?: string[];
    UserOrganizationRoles?: string[];
  };
  Power?: UserPower;
  Status?: number;
}

export interface DoctorAuthenticationParams {
  workflowId: string;
  userId: string;
  node?: string;
  remark?: string;
  reason?: string;
}
