import request from "@/utils/request";
const PASSPORT_INVITER = "/passport/inviter";

export interface InviterDTO {
  Id: string;
  InviterName: string;
  OrganizationId: string;
  OrganizationName: string;
  CreatedTime: string;
  QrCode?: string;
  Remark?: string;
  Type?: string;
}

/** 获取全部机构推广码 */
export const getInviterList = (params: PageParams) => {
  return request.get<ListDataTotalCount<InviterDTO>>(`${PASSPORT_INVITER}/GetList`, {
    params,
  });
};

/** 添加或者编辑机构推广码入参 */
export type InviterAddOrUpdateInput = RequireKeys<InviterDTO, "InviterName" | "OrganizationId">;

/** 添加或者编辑机构推广码 */
export function addOrUpdateInviter(data: InviterAddOrUpdateInput, type: "Add" | "Update") {
  return request.post(`${PASSPORT_INVITER}/${type}`, data);
}
