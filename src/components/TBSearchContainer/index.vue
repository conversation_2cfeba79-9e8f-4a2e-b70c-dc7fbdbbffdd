<template>
  <div class="tb-container">
    <div class="tb-content" :class="{ [`is-collapsed_${size}`]: !isExpanded }">
      <slot name="left" class="tb-container-left" />
      <div class="tb-container-right">
        <slot name="right" />
      </div>
    </div>
    <div v-if="isShowToggle" class="tb-toggle" @click="toggleExpand">
      <span class="toggle-text">{{ isExpanded ? "收起" : "展开" }}</span>
      <i class="toggle-icon" :class="{ 'is-expanded': isExpanded }" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { DeviceEnum } from "@/enums/DeviceEnum";
import { useAppStore } from "@/store";
const appStore = useAppStore();
const isExpanded = ref<boolean>(true);
const size = ref<string>(appStore.size);
interface Props {
  isShowToggle?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isShowToggle: false,
});
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};
const isShowToggle = ref<boolean>(props.isShowToggle);
// 监听appStore.size变化
watch(
  () => appStore.size,
  (newSize) => {
    size.value = newSize;
  },
  { immediate: true }
);
// 监听appStore.device变化
watch(
  () => appStore.device,
  (newDevice) => {
    if (newDevice === DeviceEnum.MOBILE) {
      isShowToggle.value = true;
      isExpanded.value = false;
    } else {
      isExpanded.value = true;
    }
  },
  { immediate: true }
);
</script>
<style scoped lang="scss">
.tb-container {
  padding: 10px;
  padding-bottom: 0;
  position: relative;

  .tb-content {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    transition: all 0.3s ease;
    overflow: hidden;

    &.is-collapsed_large {
      max-height: 48px;
    }
    &.is-collapsed_default {
      max-height: 38px;
    }
    &.is-collapsed_small {
      max-height: 28px;
    }
  }

  &-left {
    flex: 1;
  }

  &-right {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
  }

  .tb-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #666;
    font-size: 14px;

    &:hover {
      color: #1890ff;
    }

    .toggle-text {
      margin-right: 4px;
    }

    .toggle-icon {
      width: 12px;
      height: 12px;
      position: relative;
      transition: transform 0.3s ease;

      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 6px 6px 0 6px;
        border-color: currentColor transparent transparent transparent;
        transform: translate(-50%, -50%);
      }

      &.is-expanded {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
