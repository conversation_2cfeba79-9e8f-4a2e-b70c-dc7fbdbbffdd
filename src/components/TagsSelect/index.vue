<template>
  <div class="diagnosis-input">
    <el-tag
      v-for="item in tags"
      :key="item[pageProps.props.value]"
      class="mx-5px"
      :closable="!disabled"
      @close="handleRemove(item)"
    >
      {{ item[pageProps.props.label] }}
    </el-tag>
    <el-button v-if="!showSelect" :disabled="disabled" type="primary" @click="handleShowSelect">
      添加
    </el-button>
    <el-select
      v-else
      ref="selectRef"
      v-model="selectedItems"
      multiple
      filterable
      :disabled="disabled"
      placeholder="请选择"
      style="width: 240px"
      @blur="handleSelectBlur"
      @visible-change="handleVisibleChange"
    >
      <el-option
        v-for="item in options"
        :key="item[pageProps.props.value]"
        :label="item[pageProps.props.label]"
        :value="item[pageProps.props.value]"
      />
    </el-select>
  </div>
</template>

<script setup lang="ts">
const tags = ref<any[]>([]);
const options = ref<any[]>([]);
const showSelect = ref<boolean>(false);
const selectRef = ref();
const selectedItems = ref<any[]>([]);

const emit = defineEmits<{
  (e: "update:modelValue", value: any[]): void;
  (e: "change", value: any[]): void;
}>();
// 移除已选
const handleRemove = (deleteItem: any) => {
  tags.value = tags.value.filter(
    (item) => item[pageProps.props.value] !== deleteItem[pageProps.props.value]
  );
  handleSelectChange();
};
// 显示选择器
const handleShowSelect = async () => {
  showSelect.value = true;
  await nextTick();
  const select = selectRef.value;
  select?.focus();
  if (select) {
    select.expanded = true;
  }
};

// 处理选择框失焦
const handleSelectBlur = () => {
  const findSelectedItems = selectedItems.value.map((item: any) =>
    options.value.find((option) => option[pageProps.props.value] === item)
  );

  // 过滤掉已存在的诊断
  const uniqueDiagnoses = findSelectedItems.filter(
    (newItem) =>
      !tags.value.some(
        (existingItem) => existingItem[pageProps.props.value] === newItem[pageProps.props.value]
      )
  );

  // 添加诊断
  tags.value.push(...uniqueDiagnoses);

  // 清空选择并隐藏select
  selectedItems.value = [];
  showSelect.value = false;
  handleSelectChange();
};

// 处理西医诊断下拉框显示状态变化
const handleVisibleChange = (visible: boolean) => {
  if (!visible) {
    showSelect.value = false;
  }
};

const handleCheckSourceDataIsSame = () => {
  if (pageProps.isBackOnlyKey) {
    tags.value = pageProps.options.filter((s) =>
      pageProps.modelValue.some((k) => k === s[pageProps.props.value])
    );
  } else {
    // 检查tags和options是否都有props.value和props.label
    const hasValue = tags.value.every((item) => item[pageProps.props.value]);
    const hasLabel = tags.value.every((item) => item[pageProps.props.label]);
    if (!hasValue || !hasLabel) {
      throw new Error(`tags和options中缺少${pageProps.props.value}或${pageProps.props.label}`);
    }
  }
};

const handleSelectChange = () => {
  emit(
    "update:modelValue",
    pageProps.isBackOnlyKey ? tags.value.map((item) => item[pageProps.props.value]) : tags.value
  );
  emit("change", tags.value);
};

interface Props<T> {
  props: {
    label: string;
    value: string;
  };
  options: T[];
  modelValue: any[];
  disabled?: boolean;
  isBackOnlyKey?: boolean;
}
const pageProps = defineProps<Props<any>>();

watch(
  () => [pageProps.modelValue, pageProps.options],
  ([newTags, newOptions]) => {
    options.value = newOptions || [];
    tags.value = newTags || [];
    if (newTags.length && newOptions.length) {
      handleCheckSourceDataIsSame();
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss"></style>
