<template>
  <div class="text-center h-500px max-w-700px">
    <el-transfer
      ref="transferRef"
      v-model="selectedIds"
      class="flex items-center h-full!"
      filterable
      :filter-method="filterMethod"
      filter-placeholder="请输入医院名称"
      :data="sourceData"
      :titles="['待选项', '已选项']"
      :props="{
        key: 'Id',
        label: 'Name',
      }"
      @left-check-change="handleLeftCheckChange"
      @right-check-change="handleRightCheckChange"
    />
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end mt-20px">
    <el-button @click="$emit('cancel')">取消</el-button>
    <el-button type="primary" :loading="loading" @click="onSubmit">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";
import { TransferDataItem, TransferKey } from "element-plus";

defineOptions({
  name: "HospitalTransfer",
});

const emit = defineEmits<{
  (e: "selection-change", value: string[]): void;
  (e: "cancel"): void;
  (e: "submit", value: string[]): void;
}>();

const props = defineProps<{
  /** 已选机构id */
  value?: string[];
  /** 确认按钮，是否正在加载中 */
  loading?: boolean;
}>();

const transferRef = useTemplateRef("transferRef");
const sourceData = ref<BaseOrganization[]>([]);
const selectedIds = ref<string[]>([]);

const filterMethod = (query: string, item: TransferDataItem) => {
  return item.Name.toLowerCase().includes(query.toLowerCase());
};

const handleGetOrganizationList = async () => {
  try {
    const res = await Passport_Api.getOrganizationList({
      IsEnabled: true,
      Keyword: "",
      DtoTypeName: "QueryOrgDTOForDropDownList",
      PageIndex: 1,
      PageSize: 9999,
      Scopeable: false,
    });
    if (res.Type === 200) {
      sourceData.value = res.Data.Rows;
    }
  } catch (error) {
    console.log(error);
  }
};

async function handleLeftCheckChange(checkedKeys: TransferKey[], movedKeys?: TransferKey[]) {
  console.log("left", checkedKeys, movedKeys);

  // 更新右侧列表
  selectedIds.value = Array.from(
    new Set([...selectedIds.value, ...checkedKeys.map((item) => item.toString())])
  );

  // 清空左侧选中状态
  await nextTick();
  if (transferRef.value) {
    (transferRef.value as any).leftChecked = [];
  }

  emit("selection-change", selectedIds.value);
}

async function handleRightCheckChange(checkedKeys: TransferKey[], movedKeys?: TransferKey[]) {
  console.log("right", checkedKeys, movedKeys);

  // 更新左侧列表
  selectedIds.value = selectedIds.value.filter((key) => !checkedKeys.includes(key));

  // 清空右侧选中状态
  await nextTick();
  if (transferRef.value) {
    (transferRef.value as any).rightChecked = [];
  }

  emit("selection-change", selectedIds.value);
}

// 确定
function onSubmit() {
  if (!selectedIds.value?.length) {
    ElMessage.warning("请选择需要推送的机构");
    return;
  }

  emit("submit", selectedIds.value);
}

onMounted(async () => {
  await handleGetOrganizationList();
  selectedIds.value.push(...(props.value ?? []));
});
</script>

<style scoped lang="scss">
// 修改transfer组件样式
:deep(.el-transfer-panel) {
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.el-transfer-panel__body) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-transfer-panel__list.is-filterable) {
  flex: 1;
}

:deep(.el-transfer-panel .el-transfer-panel__empty) {
  flex: 1;
  height: 100%;
}

:deep(.el-transfer__buttons) {
  width: 0;
  height: 0;
  overflow: hidden;
  padding: 0 20px;
}

:deep(.el-transfer-panel__item) {
  display: flex !important;
  align-items: center;
  padding: 8px 0 8px 15px;
  height: auto;
}

:deep(.el-checkbox) {
  margin-right: 15px;
}

:deep(.el-transfer-panel__item .el-checkbox__input) {
  position: relative;
  top: 0;
}

:deep(.el-transfer-panel__item.el-checkbox .el-checkbox__label) {
  white-space: normal;
  line-height: 1.2;
  padding-left: 10px;
}
</style>
