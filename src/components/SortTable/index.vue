<template>
  <div
    v-loading="props.loading"
    :style="{ width: `${props.width}px`, height: `${props.height}px` }"
    class="table-v2-wrapper"
  >
    <el-table-v2
      v-model:sort-state="sortState"
      :row-key="props.rowKey"
      :header-height="props.headerHeight"
      :estimated-row-height="props.estimatedRowHeight"
      :columns="tableColumns"
      :data="tableData"
      :width="props.width - 2"
      :height="props.height - 2"
      fixed
      @column-sort="onSort"
    >
      <template #header-cell="{ column }">
        <slot name="header-cell" :column="column">
          <el-text class="text-#909399! font-600!">{{ column.title }}</el-text>
        </slot>
      </template>
      <template #cell="{ column, rowData }">
        <slot name="cell" :column="column" :row-data="rowData">
          <el-text class="py-6px!">{{ rowData[column.key] }}</el-text>
        </slot>
      </template>
    </el-table-v2>
  </div>
</template>

<script setup lang="tsx" generic="T extends Record<string, any>">
import { ElButton, TableV2SortOrder, ElText } from "element-plus";
import type { SortState, SortBy, Column } from "element-plus";
import type { TableV2Column, OperationsTableV2Column } from "./types";

const kEnableDebug = false;
defineOptions({
  name: "SortTable",
});

const props = withDefaults(
  defineProps<{
    height: number;
    width: number;
    /**
     * 表格列配置
     */
    columns: TableV2Column<T>[];
    /**
     * 表格数据
     */
    data: T[];
    loading?: boolean;
    /**
     * 行数据唯一标识，默认id
     */
    rowKey?: string;
    /**
     * 表头高度，默认50
     */
    headerHeight?: number;
    /**
     * 行高估算值，用于优化滚动性能，默认50
     */
    estimatedRowHeight?: number;
  }>(),
  {
    loading: false,
    rowKey: "id",
    headerHeight: 70,
    estimatedRowHeight: 50,
  }
);

/** 表格数据 */
const tableData = ref<T[]>([]);
watch(
  () => props.data,
  (newVal) => {
    tableData.value = newVal;
  }
);

/** 表格列 */
const tableColumns = ref<Column<T>[]>(
  props.columns.map((column) => ({
    key: column.key as string,
    title: column.title,
    dataKey: column.key,
    width: column.width,
    minWidth: column.minWidth,
    maxWidth: column.maxWidth,
    align: "center",
    fixed: column.fixed,
    sortable: column.sortable,
    cellRenderer: column.cellRenderer
      ? ({ rowData, rowIndex }) => column.cellRenderer!({ rowData, rowIndex })
      : column.cellBuilder
        ? ({ rowData }) => (
            <ElText class="text-#909399! font-600!">{column.cellBuilder!(rowData) ?? ""}</ElText>
          )
        : hasOperations(column)
          ? ({ rowData }) => (
              <>
                {column.operations.map(({ label, type, disabled, onClick }) => {
                  return (
                    <ElButton
                      type={type ?? "primary"}
                      link
                      disabled={disabled}
                      onClick={() => onClick?.(rowData)}
                      class="mx-5px!"
                    >
                      {label}
                    </ElButton>
                  );
                })}
              </>
            )
          : undefined,
    headerCellRenderer: column.headerCellRenderer
      ? ({ columnIndex }) => column.headerCellRenderer!({ column, columnIndex })
      : undefined,
  }))
);

/**
 * 对表格中某列数据排序
 *
 * @param key 排序字段
 * @param order 排序顺序
 */
function sortData(key: string, order: TableV2SortOrder) {
  const compareValue = (value: any) => {
    if (typeof value === "string" && value === "是") {
      return 1;
    } else if (typeof value === "string" && value === "否") {
      return 0;
    } else if (typeof value === "string") {
      return Number(value) ?? 0;
    } else {
      return value;
    }
  };

  tableData.value = tableData.value?.sort((a, b) => {
    return order === TableV2SortOrder.ASC
      ? compareValue(a[key]) - compareValue(b[key])
      : compareValue(b[key]) - compareValue(a[key]);
  });
}

/**
 * 排序事件
 *
 * @param key 排序字段
 * @param order 排序顺序
 */
function onSort({ key, order }: SortBy) {
  kEnableDebug && console.debug("排序", key, order);
  sortState.value[key as string] = order;

  const sortMethod = props.columns.find((column) => column.key === key)?.sortMethod;
  if (sortMethod) {
    tableData.value = sortMethod(order);
  } else {
    sortData(key as string, order);
  }
}

/** 需要排序的列 */
const sortState = ref<SortState>(
  props.columns
    .filter((column) => column.sortable)
    .reduce(
      (acc, column) => {
        acc[column.key] = column.sortOrder ?? TableV2SortOrder.DESC;
        return acc;
      },
      {} as Record<string, TableV2SortOrder>
    )
);

/**
 * 类型守卫函数：检查column是否包含operations属性
 */
function hasOperations<T extends Record<string, any>>(
  column: TableV2Column<T>
): column is OperationsTableV2Column<T> {
  return "operations" in column && Array.isArray(column.operations) && column.operations.length > 0;
}
</script>

<style lang="scss" scoped>
.table-v2-wrapper {
  border: 1px solid #ebeef5;
  position: relative;
}

:deep(.el-table-v2__header-cell) {
  border-left: 1px solid #ebeef5;
}

:deep(.el-table-v2__row-cell) {
  border-left: 1px solid #ebeef5;
}
</style>
