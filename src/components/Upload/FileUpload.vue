<!-- 文件上传组件 -->
<template>
  <div>
    <el-upload
      v-model:file-list="fileList"
      list-type="picture-card"
      :before-upload="handleBeforeUpload"
      :http-request="handleUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-exceed="handleExceed"
      :accept="props.accept"
      :limit="props.limit"
      multiple
      :disabled="disabled"
    >
      <el-icon><Plus /></el-icon>
      <template #file="{ file }">
        <div class="file-card">
          <!-- 文件图标或视频预览 -->
          <div class="file-preview">
            <video
              v-if="isVideoFile(file.name)"
              :src="file.url"
              class="video-thumbnail"
              controls
              preload="metadata"
            />
            <div v-else class="file-icon">
              <el-icon size="40"><Document /></el-icon>
              <div class="file-extension">{{ getFileExtension(file.name) }}</div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <span class="el-upload-list__item-actions">
            <!-- 预览按钮 -->
            <span v-if="isVideoFile(file.name)" @click="handlePreviewVideo(file.url!)">
              <el-icon><VideoPlay /></el-icon>
            </span>
            <!-- 下载按钮 -->
            <!-- <span @click="handleDownload(file)">
              <el-icon><Download /></el-icon>
            </span> -->
            <!-- 删除按钮 -->
            <span v-if="!disabled" @click="handleRemove(file.url!)">
              <el-icon><Delete /></el-icon>
            </span>
          </span>
        </div>
      </template>
    </el-upload>

    <!-- 视频预览对话框 -->
    <el-dialog
      v-model="videoPreviewVisible"
      title="预览"
      width="80%"
      :before-close="handleVideoPreviewClose"
    >
      <div class="video-preview-container">
        <video
          v-if="previewVideoUrl"
          :src="previewVideoUrl"
          controls
          autoplay
          class="preview-video"
        />
      </div>
    </el-dialog>

    <el-progress v-if="showProgress" :percentage="progressPercent" style="margin-top: 10px" />
  </div>
</template>
<script lang="ts" setup>
import { UploadRawFile, UploadUserFile, UploadRequestOptions } from "element-plus";

import FileAPI, { FileInfo } from "@/api/file";

const props = defineProps({
  /**
   * 请求携带的额外参数
   */
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  /**
   * 上传文件的参数名
   */
  name: {
    type: String,
    default: "file",
  },
  /**
   * 文件上传数量限制
   */
  limit: {
    type: Number,
    default: 10,
  },
  /**
   * 单个文件上传大小限制(单位MB)
   */
  maxFileSize: {
    type: Number,
    default: 10,
  },
  /**
   * 上传文件类型 (排除图片文件)
   */
  accept: {
    type: String,
    default:
      ".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar,.mp4,.avi,.mov,.wmv,.flv,.mkv,.webm",
  },
  /**
   * 上传按钮文本
   */
  uploadBtnText: {
    type: String,
    default: "上传文件",
  },

  /**
   * 样式
   */
  style: {
    type: Object,
    default: () => {
      return {
        width: "300px",
      };
    },
  },
  /**
   * 是否禁用上传
   */
  disabled: {
    type: Boolean,
    default: false,
  },
});

const modelValue = defineModel("modelValue", {
  type: [Array] as PropType<string[]>,
  required: true,
  default: () => [],
});

const fileList = ref([] as UploadUserFile[]);

const showProgress = ref(false);
const progressPercent = ref(0);

// 视频预览相关
const videoPreviewVisible = ref(false);
const previewVideoUrl = ref("");

/**
 * 上传前校验
 */
function handleBeforeUpload(file: UploadRawFile) {
  // 校验文件类型：排除图片文件
  if (file.type.startsWith("image/")) {
    ElMessage.warning("不支持上传图片文件，请选择其他类型的文件");
    return false;
  }

  // 校验文件类型是否符合 accept 规则
  const acceptTypes = props.accept.split(",").map((type) => type.trim());
  const isValidType = acceptTypes.some((type) => {
    if (type.startsWith(".")) {
      // 如果是扩展名 (.pdf, .doc)，检查文件名是否以指定扩展名结尾
      return file.name.toLowerCase().endsWith(type.toLowerCase());
    } else {
      // 如果是具体的 MIME 类型，检查是否完全匹配
      return file.type === type;
    }
  });

  if (!isValidType) {
    ElMessage.warning(`上传文件的格式不正确，仅支持：${props.accept}`);
    return false;
  }

  // 限制文件大小
  if (file.size > props.maxFileSize * 1024 * 1024) {
    ElMessage.warning("上传文件不能大于" + props.maxFileSize + "M");
    return false;
  }
  return true;
}

/*
 * 上传文件
 */
function handleUpload(options: UploadRequestOptions) {
  return new Promise((resolve, reject) => {
    const file = options.file;

    const formData = new FormData();
    formData.append(props.name, file);

    // 处理附加参数
    Object.keys(props.data).forEach((key) => {
      formData.append(key, props.data[key]);
    });

    FileAPI.upload(formData)
      .then((data) => {
        if (data.Type === 200) {
          resolve({
            name: file.name,
            url: data.Data.HostSetting.External + data.Data.PathSetting.Path,
          });
        } else {
          reject({
            name: file.name,
            url: "",
          });
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
}

/**
 * 上传文件超出限制
 */
function handleExceed() {
  ElMessage.warning("最多只能上传" + props.limit + "个文件");
}

/**
 * 上传成功回调
 */
const handleSuccess = (fileInfo: FileInfo, uploadFile: UploadUserFile) => {
  ElMessage.success("上传成功");
  const index = fileList.value.findIndex((file) => file.uid === uploadFile.uid);
  if (index !== -1) {
    fileList.value[index].url = fileInfo.url;
    fileList.value[index].status = "success";
    modelValue.value[index] = fileInfo.url;
  }
};

/**
 * 上传失败回调
 */
const handleError = (error: any) => {
  ElMessage.error("上传失败: " + error.message);
};

/**
 * 删除文件
 */
function handleRemove(fileUrl: string) {
  if (props.disabled) {
    return;
  }
  const index = modelValue.value.indexOf(fileUrl);
  if (index !== -1) {
    modelValue.value.splice(index, 1);
    fileList.value.splice(index, 1);
  }
}

/**
 * 下载文件
 */
function handleDownload(file: UploadUserFile) {
  if (file.url) {
    const link = document.createElement("a");
    link.href = file.url;
    link.download = file.name || "download";
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

/**
 * 判断是否为视频文件
 */
function isVideoFile(fileName: string): boolean {
  const videoExtensions = [".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv", ".webm"];
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf("."));
  return videoExtensions.includes(extension);
}

/**
 * 获取文件扩展名
 */
function getFileExtension(fileName: string): string {
  const extension = fileName.substring(fileName.lastIndexOf(".") + 1);
  return extension.toUpperCase();
}

/**
 * 预览视频
 */
function handlePreviewVideo(videoUrl: string) {
  previewVideoUrl.value = videoUrl;
  videoPreviewVisible.value = true;
}

/**
 * 关闭视频预览
 */
function handleVideoPreviewClose() {
  videoPreviewVisible.value = false;
  previewVideoUrl.value = "";
}

watch(modelValue, (value) => {
  fileList.value = value.map((url) => {
    const name = url.substring(url.lastIndexOf("/") + 1);
    return {
      name: name,
      url: url,
    } as UploadUserFile;
  });
});
</script>
<style lang="scss" scoped>
.file-card {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--el-bg-color);
  border-radius: 6px;
  transition: all 0.3s;

  &:hover {
    background: var(--el-bg-color-page);
  }
}

.file-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.file-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--el-text-color-regular);
}

.file-extension {
  font-size: 12px;
  font-weight: bold;
  margin-top: 4px;
  color: var(--el-color-primary);
}

.file-name {
  width: 100%;
  font-size: 12px;
  text-align: center;
  color: var(--el-text-color-regular);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 8px;
}

.el-upload-list__item-actions {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 6px;

  span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    cursor: pointer;
    color: var(--el-text-color-primary);
    transition: all 0.3s;

    &:hover {
      background: var(--el-color-primary);
      color: white;
    }
  }
}

.file-card:hover .el-upload-list__item-actions {
  opacity: 1;
}

.video-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 60vh;
}

.preview-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

:deep(.el-upload-list) {
  margin: 0;
}

:deep(.el-upload-list__item) {
  margin-right: 10px;
  border: none;
  background: transparent;
}

:deep(.el-upload--picture-card) {
  --el-upload-picture-card-size: 148px;
}
</style>
