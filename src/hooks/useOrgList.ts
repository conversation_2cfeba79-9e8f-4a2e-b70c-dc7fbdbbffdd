import Passport_Api from "@/api/passport";
import { type AllOrganizationListInputDTO } from "@/api/passport/types";

type OrgOption = RequireKeys<BaseOrganization, "Id" | "Name">;

export default (options?: { hasAll?: boolean; allOption?: OrgOption }) => {
  /** 是否包含全部机构 */
  const hasAll = options?.hasAll ?? true;
  /** 全部机构选项 */
  const allOption = options?.allOption ?? { Id: "*", Name: "全部机构" };

  /** 机构列表 */
  const baseOrganizationList = ref<OrgOption[]>([]);

  /** 加载机构列表 */
  const loadOrgList = async (params?: AllOrganizationListInputDTO) => {
    let data: AllOrganizationListInputDTO = {
      IsEnabled: true,
      Keyword: "",
      DtoTypeName: "QueryOrgDtoForDropDownList", // QueryOrgDetailOutputDto1
      Scopeable: false,
      IsTreatment: undefined,
    };
    if (params) {
      data = { ...data, ...params };
    }

    const res = await Passport_Api.getAllOrganizationList(data);
    if (res.Type === 200) {
      baseOrganizationList.value.push(
        ...res.Data.map((item) => ({
          ...item,
          Id: item.Id ?? "",
          Name: item.Name ?? "",
        }))
      );
    }
  };

  /** 重置机构列表 */
  const resetOrgList = () => {
    if (hasAll) {
      baseOrganizationList.value = [allOption];
    } else {
      baseOrganizationList.value = [];
    }
  };

  resetOrgList();

  return {
    baseOrganizationList,
    loadOrgList,
    resetOrgList,
  };
};
