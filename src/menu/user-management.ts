import { type RouteVO } from "@/api/system/menu";

const userManagementRoutes: RouteVO[] = [
  {
    path: "/user-management",
    component: "Layout",
    meta: {
      title: "用户管理",
      icon: "system",
      hidden: false,
      alwaysShow: false,
      roles: [
        "scienceTechnologyExhibition",
        "superOperate",
        "assistant",
        "sales",
        "internetHospitalAdmin",
        "operations",
        "superOperate",
        "promoter",
        "externalSeller",
      ],
    },
    children: [
      {
        path: "medicalConsortium",
        component: "user-management/medicalConsortium/index",
        name: "MedicalConsortium",
        meta: {
          title: "医联体",
          icon: "el-icon-OfficeBuilding",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["operations"],
        },
      },
      {
        path: "hospitalManagement",
        component: "user-management/hospitalManagement/index",
        name: "HospitalManagement",
        meta: {
          title: "医院管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["assistant", "operations"],
        },
      },
      {
        path: "userManagement",
        component: "user-management/userManagement/index",
        name: "UserManagement",
        meta: {
          title: "用户管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["assistant", "operations"],
        },
      },
      {
        path: "doctorCertificationReview",
        component: "user-management/doctorCertificationReview/index",
        name: "DoctorCertificationReview",
        meta: {
          title: "医生认证审核",
          icon: "el-icon-Postcard",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "assistant", "operations", "scienceTechnologyExhibition"],
        },
      },

      {
        path: "patientCertificationReview",
        component: "user-management/patientCertificationReview/index",
        name: "PatientCertificationReview",
        meta: {
          title: "患者认证审核",
          icon: "el-icon-Postcard",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "operations", "scienceTechnologyExhibition"],
        },
      },
      {
        path: "doctorQuery",
        component: "user-management/doctorQuery/index",
        name: "DoctorQuery",
        meta: {
          title: "医生信息管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["assistant", "operations", "promoter", "internetHospitalAdmin", "externalSeller"],
        },
      },
      {
        path: "settlementRatio",
        component: "user-management/settlementRatio/index",
        name: "SettlementRatio",
        meta: {
          title: "结算比例管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["finance"],
        },
      },
      {
        path: "uploadSupervisingDoctor",
        component: "user-management/uploadSupervisingDoctor/index",
        name: "UploadSupervisingDoctor",
        meta: {
          title: "上传监管医生",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "userQuery",
        component: "user-management/userQuery/index",
        name: "UserQuery",
        meta: {
          title: "普通用户查询",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["assistant", "sales", "internetHospitalAdmin", "operations", "superOperate"],
        },
      },
      {
        path: "roleManagement",
        component: "user-management/roleManagement/index",
        name: "RoleManagement",
        meta: {
          title: "角色管理",
          icon: "el-icon-User",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["operations"],
        },
      },
      {
        path: "patientInquiry",
        component: "user-management/patientInquiry/index",
        name: "PatientInquiry",
        meta: {
          title: "患者查询",
          icon: "el-icon-User",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: [
            "assistant",
            "sales",
            "internetHospitalAdmin",
            "operations",
            "superOperate",
            "promoter",
            "externalSeller",
          ],
        },
      },
      {
        path: "TestUser",
        name: "TestUser",
        component: "user-management/TestUser",
        meta: {
          title: "测试用户管理",
          icon: "el-icon-User",
          roles: ["assistant", "operations"],
        },
      },
      {
        path: "GeneralizationCode",
        name: "GeneralizationCode",
        component: "user-management/GeneralizationCode",
        meta: { title: "试验区管理", roles: ["operations"] },
      },
    ],
  },
];
export default userManagementRoutes;
