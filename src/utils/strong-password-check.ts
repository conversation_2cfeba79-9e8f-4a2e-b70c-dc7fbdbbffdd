/**
 * 强密码正则
 */
const STRONG_PASSWORD_REGEXP =
  /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~])[\da-zA-Z!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~]{6,12}$/;

const STRONG_PASSWORD_WARN_TEXT =
  "密码至少包含一个数字、一个小写字母、一个大写字母和一个特殊字符，并在6到12位之间";

class StrongPasswordCheck {
  private reg: RegExp;
  private warnText: string;

  constructor() {
    this.reg = STRONG_PASSWORD_REGEXP;
    this.warnText = STRONG_PASSWORD_WARN_TEXT;
  }

  /**
   * 检查密码是否符合强密码规则
   *
   * @param {string} password 密码
   * @returns {boolean} 是否符合强密码规则
   */
  check(password: string): boolean {
    return this.reg.test(password);
  }

  getWarnText(): string {
    return this.warnText;
  }
}

export default new StrongPasswordCheck();
