import defaultSettings from "@/settings";

// 导入 Element Plus 中英文语言包
import zhCn from "element-plus/es/locale/lang/zh-cn";
import en from "element-plus/es/locale/lang/en";
import { store } from "@/store";
import { DeviceEnum } from "@/enums/DeviceEnum";
import { SidebarStatusEnum } from "@/enums/SidebarStatusEnum";
import { useSessionStorage } from "@vueuse/core";
import PassportAPI from "@/api/passport";
import Bff_Api from "@/api/bff";
import { setupKeywordRegexp } from "@/utils/request";
import { DEFAULT_DYNAMIC_CONFIG } from "@/utils/constants";

export const useAppStore = defineStore("app", () => {
  // 设备类型
  const device = useSessionStorage("device", DeviceEnum.DESKTOP);
  // 布局大小
  const size = useSessionStorage("size", defaultSettings.size);
  // 语言
  const language = useSessionStorage("language", defaultSettings.language);
  // 侧边栏状态
  const sidebarStatus = useSessionStorage("sidebarStatus", SidebarStatusEnum.CLOSED);
  const sidebar = reactive({
    opened: sidebarStatus.value === SidebarStatusEnum.OPENED,
    withoutAnimation: false,
  });

  // 顶部菜单激活路径
  const activeTopMenuPath = useSessionStorage("activeTopMenuPath", "");

  // 是否锁屏
  const isLocked = useSessionStorage("isLocked", false);

  /**
   * 根据语言标识读取对应的语言包
   */
  const locale = computed(() => {
    if (language?.value == "en") {
      return en;
    } else {
      return zhCn;
    }
  });

  // 切换侧边栏
  function toggleSidebar() {
    sidebar.opened = !sidebar.opened;
    sidebarStatus.value = sidebar.opened ? SidebarStatusEnum.OPENED : SidebarStatusEnum.CLOSED;
  }

  // 关闭侧边栏
  function closeSideBar() {
    sidebar.opened = false;
    sidebarStatus.value = SidebarStatusEnum.CLOSED;
  }

  // 打开侧边栏
  function openSideBar() {
    sidebar.opened = true;
    sidebarStatus.value = SidebarStatusEnum.OPENED;
  }

  // 切换设备
  function toggleDevice(val: string) {
    device.value = val;
  }

  /**
   * 改变布局大小
   *
   * @param val 布局大小 default | small | large
   */
  function changeSize(val: string) {
    size.value = val;
  }
  /**
   * 切换语言
   *
   * @param val
   */
  function changeLanguage(val: string) {
    language.value = val;
  }
  /**
   * 混合模式顶部切换
   */
  function activeTopMenu(val: string) {
    activeTopMenuPath.value = val;
  }

  // 设置锁屏状态
  function setLockState(locked: boolean) {
    isLocked.value = locked;
  }

  const clientToken = ref("");
  async function getClientToken() {
    if (clientToken.value) {
      return clientToken.value;
    } else {
      const res = await PassportAPI.generateToken({
        appId: "PlatformOfWeb",
        appSecret: "secret",
      });
      if (res.Type === 200) {
        clientToken.value = res.Data.token_type + " " + res.Data.access_token;
      }
    }
    return clientToken.value;
  }

  const dynamicConfig = ref<DynamicConfig>({ ...DEFAULT_DYNAMIC_CONFIG });

  async function loadDynamicConfig() {
    const token = await getClientToken();
    const res = await Bff_Api.getSettingWithoutAuth({ Filter: { Category: "Web" } }, token);
    if (res.Type === 200) {
      dynamicConfig.value = { ...DEFAULT_DYNAMIC_CONFIG, ...res.Data[0].Data };
      if (res.Data[0].Data.keywordRegexp) {
        setupKeywordRegexp(new RegExp(res.Data[0].Data.keywordRegexp));
      }
    } else {
      console.warn("获取强制登录配置失败", res);
    }
  }

  return {
    device,
    sidebar,
    language,
    locale,
    size,
    isLocked,
    activeTopMenu,
    toggleDevice,
    changeSize,
    changeLanguage,
    toggleSidebar,
    closeSideBar,
    openSideBar,
    activeTopMenuPath,
    setLockState,
    getClientToken,
    loadDynamicConfig,
    dynamicConfig,
  };
});

/**
 * 用于在组件外部（如在Pinia Store 中）使用 Pinia 提供的 store 实例。
 * 官方文档解释了如何在组件外部使用 Pinia Store：
 * https://pinia.vuejs.org/core-concepts/outside-component-usage.html#using-a-store-outside-of-a-component
 */
export function useAppStoreHook() {
  return useAppStore(store);
}
