import { store, useTagsViewStoreHook } from "@/store";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { useDictStoreHook } from "@/store/modules/dict";

import { setToken, clearToken, setRefreshToken } from "@/utils/auth";
import Passport_Api from "@/api/passport";
import router from "@/router";
import { useProtectionStoreHook } from "@/store/modules/protection";

/**
 * 解析 redirect 字符串 为 path 和  queryParams
 *
 * @returns { path: string, queryParams: Record<string, string> } 解析后的 path 和 queryParams
 */
function parseRedirect(): {
  path: string;
  queryParams: Record<string, string>;
} {
  const query = router.currentRoute.value.query;
  const redirect = (query.redirect as string) ?? "/";

  const url = new URL(redirect, window.location.origin);
  const path = url.pathname;
  const queryParams: Record<string, string> = {};

  url.searchParams.forEach((value, key) => {
    queryParams[key] = value;
  });

  console.log("origin", window.location.origin, "query", query, "redirect", redirect);

  return { path, queryParams };
}

export const useUserStore = defineStore("user", () => {
  const userInfo = useSessionStorage<BaseUserProfile>("userInfo", {} as BaseUserProfile);
  /**
   * 登录的时候获取用户 token
   */
  function getUserToken(username: string, password: string) {
    return new Promise<void>((resolve, reject) => {
      Passport_Api.getUserToken(username, password)
        .then((data) => {
          console.log("getUserToken", data);

          const { Data } = data;
          setToken(Data.access_token);
          setRefreshToken(Data.refresh_token);
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * 获取用户信息
   */
  function getUserInfo() {
    return new Promise<BaseUserProfile>((resolve, reject) => {
      Passport_Api.getUserProfileGetMethod()
        .then((data) => {
          if (!data) {
            reject("Verification failed, please Login again.");
            return;
          }
          Object.assign(userInfo.value, { ...data.Data });
          resolve(data.Data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  async function loginWithToken(token: string, refreshToken?: string) {
    setToken(token);
    if (refreshToken) {
      setRefreshToken(refreshToken);
    }
    await getUserInfo();
    // 跳转到登录前的页面
    const { path, queryParams } = parseRedirect();
    console.log(path);
    router.push({ path: path, query: queryParams });
  }

  /**
   * 登出
   */
  function logout() {
    return new Promise<void>((resolve) => {
      clearUserData();
      resolve();
      useProtectionStoreHook().reset();
    });
  }

  function logoutImmediately() {
    logout()
      .then(() => {
        useTagsViewStoreHook().delAllViews();
      })
      .then(() => {
        router.push(`/login`);
      });
  }

  // /**
  //  * 刷新 token
  //  */
  // function refreshToken() {
  //   const refreshToken = getRefreshToken();
  //   return new Promise<void>((resolve, reject) => {
  //     AuthAPI.refreshToken(refreshToken)
  //       .then((data) => {
  //         // const { accessToken, refreshToken } = data;
  //         // setToken(accessToken);
  //         // setRefreshToken(refreshToken);
  //         resolve();
  //       })
  //       .catch((error) => {
  //         console.log(" refreshToken  刷新失败", error);
  //         reject(error);
  //       });
  //   });
  // }

  /**
   * 清理用户数据
   *
   * @returns
   */
  function clearUserData() {
    return new Promise<void>((resolve) => {
      clearToken();
      usePermissionStoreHook().resetRouter();
      useDictStoreHook().clearDictionaryCache();
      resolve();
    });
  }

  return {
    userInfo,
    getUserInfo,
    loginWithToken,
    logout,
    logoutImmediately,
    clearUserData,
    // refreshToken,
    getUserToken,
  };
});

/**
 * 用于在组件外部（如在Pinia Store 中）使用 Pinia 提供的 store 实例。
 * 官方文档解释了如何在组件外部使用 Pinia Store：
 * https://pinia.vuejs.org/core-concepts/outside-component-usage.html#using-a-store-outside-of-a-component
 */
export function useUserStoreHook() {
  return useUserStore(store);
}
