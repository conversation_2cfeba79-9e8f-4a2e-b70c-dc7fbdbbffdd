import strongPasswordCheck from "@/utils/strong-password-check";
import { store } from "@/store";

export const useProtectionStore = defineStore("protection", () => {
  const password = useSessionStorage<string>("protection", btoa(""));
  const isWeak = computed(() => {
    return !strongPasswordCheck.check(atob(password.value));
  });
  const tipMessage = computed(() => {
    return strongPasswordCheck.getWarnText();
  });
  const setPassword = (newPassword: string) => {
    password.value = btoa(newPassword);
  };
  const reset = () => {
    password.value = btoa("");
  };
  return {
    password,
    isWeak,
    tipMessage,
    setPassword,
    reset,
  };
});

export function useProtectionStoreHook() {
  return useProtectionStore(store);
}
