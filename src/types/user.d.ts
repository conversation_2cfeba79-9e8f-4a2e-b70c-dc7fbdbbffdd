declare global {
  interface BaseUserProfile {
    Id: string;
    PhoneNumber?: string;
    Name: string;
    NickName?: string;
    UserName?: string;
    HeadImg?: string;
    IsSystem?: boolean;
    CreatedTime?: string;
    Code?: string;
    Birthday?: null;
    Sex?: string;
    IsEnabled?: boolean;
    IsLocked?: boolean;
    UserNameUpdateTime?: null;
    ChangePasswordTime?: null;
    Organization?: BaseOrganization;
    Department?: null;
    Consortiums?: null;
    Roles?: { Name: string; Id: string }[] | string[];
    UserExternalIdentify?: {
      WeChatQrCode: string;
    };
    Age?: number;
    UserWork?: UserProfileUserWork;
  }
  interface UserProfileUserWork {
    ClinicalWorkYears: string;
    ProfileDescription: string;
    QrCode: string;
  }

  interface BaseRole {
    Code?: string;
    CreatedTime?: string;
    Id?: string;
    IsAdmin?: boolean;
    IsDefault?: boolean;
    IsEnabled?: boolean;
    IsLocked?: boolean;
    IsSystem?: boolean;
    Name?: string;
    PinyinCode?: string;
    Remark?: string;
    RoleType?: string;
  }

  interface BaseDoctorItem {
    VideoCost?: number;
    RichTextCost?: number;
    ShowCost?: number;
    TreatedCount?: number;
    Doctor: BaseDoctor;
    ConsultCount?: number;
    IsEnable: boolean;
    SelfReliance: boolean;
  }

  interface BaseDoctor {
    PracticeOrganizationName: string;
    DoctorFirstAuthTime: string;
    UserId: string;
    Name: string;
    Skilled: string;
    HeadImg: string;
    WorkerTitle: string;
    OrganizationName: string;
    DepartmentName: string;
    OrganizationId: string;
    RoleTypes: string[];
    Followed: boolean;
    FollowedTime?: string;
    Sex?: string;
  }

  interface DoctorSettlementRedash {
    RoleName?: string;
    Name?: string;
    DoctorId?: string;
    PhoneNumber?: string;
    OrgName?: string;
    DeptName?: string;
    SettleAmount?: string;
    RegisterAmount?: string;
    PrescriptionAmount?: string;
    GuideAmount?: string;
    DeptChiefAmount?: string;
    PresidentAmount?: string;
    NurseChiefAmount?: string;
    RefundAmount?: string;
  }

  interface DoctorSettlementDetailRedash {
    Mode?: string;
    OrderType?: string;
    ConsultId?: string;
    OrderNo?: string;
    CreatedTime?: string;
    TotalAmount?: string;
    SettleAmount?: string;
    SettleRatio?: string;
    SettleType?: string;
  }

  interface DoctorSettlementStatisticsRedash {
    TotalAmount?: string;
  }

  interface UserOutputDTO {
    Id?: string;
    UserName?: string;
    Name?: string;
    NickName?: string;
    Email?: string;
    UserProperty?: string;
    Code?: string;
    Birthday?: string;
    Sex?: string;
    WorkerTitle?: string;
    WorkerType?: string;
    Remark1?: string;
    IsExpert?: boolean;
    HeadImg?: string;
    PhoneNumber?: string;
    IdCard?: string;
    UserWork?: UserWork;
    UserRoleDicts?: string[];
  }
}
export {};
