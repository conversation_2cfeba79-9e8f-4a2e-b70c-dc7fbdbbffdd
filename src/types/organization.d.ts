import { type EpPropMergeTypeWithNull } from "element-plus";

declare global {
  interface BaseOrganization {
    Id?: string;
    Name?: string;
    Code?: string;
    PinyinCode?: string;
    Remark?: string;
    IsLocked?: boolean;
    IsEnabled?: boolean;
    IsDefault?: boolean;
    IsTechExchange?: boolean;
    Address?: string;
    City?: EpPropMergeTypeWithNull<string>;
    CityCode?: EpPropMergeTypeWithNull<string>;
    ProvinceCode?: string;
    ProvinceName?: string;
    CountryCode?: string;
    CountryName?: string;
    Phone?: string;
    HeadImg?: string;
    SignAContractTime?: string;
    AssistantQrCode?: string;
    QrCode?: string;
    LatLon?: string;
    OrganizationPersonnel?: {
      AssistantId?: string;
      AssistantName?: string;
      BackendAssistantId?: string;
      BackendAssistantName?: string;
      ManageLeaderId?: string;
      ManageLeaderName?: string;
      SaleLeaderId?: string;
      SaleLeaderName?: string;
      CreatedTime?: string;
    };
    CityName?: string;
    Category?: number;
    Alias?: string;
    CreatedTime?: string;
    Updatable?: boolean;
    Deletable?: boolean;
    IsTreatment?: boolean;
    Work?: string;
    Carousel?: {
      ImgUrl?: string;
      SortNumber?: string;
      Title?: string;
      Url?: string;
    }[];
    Level?: number;
    OrganizationConsortiums?: {
      ConsortiumId?: string;
      ConsortiumName?: string;
      Type?: number;
    }[];
    ContractStartTime?: string;
    ContractEndTime?: string;
    AdminPhone?: string;
    CustomerServicePhone?: string;
    OrgRegisterNum?: string;
    CertificateImg?: string;
    LegalPerson?: string;
    SignAContractTime?: string;
    PlatformContractPerson?: string;
    HosContractPerson?: string;
    IsReferral?: boolean;
  }

  interface UserOrganization extends BaseOrganization {
    State?: number;
    Skilled?: string;
    SkilledTags?: string;
    Abstract?: string;
    AbstractTags?: string;
  }

  interface BaseDepartment {
    Id?: string;
    Name?: string;
    IsMain?: boolean;
    IsEnabled: boolean;
    Code?: string;
  }

  interface UserDepartment extends BaseDepartment {
    CreateOrgId?: string;
  }

  interface Consortium {
    Id?: string;
    Name?: string;
    /**
     * 管理员手机号，JSON解析后是 string[]
     * eg: "[\"13611110159\"]"
     */
    AdminPhones?: string;
    CreatedTime?: string;
  }

  interface OrganizationConsortium {
    Id?: string;
    OrganizationId?: string;
    OrganizationName?: string;
    OrganizationHeadImg?: string;
    ConsortiumId?: string;
    ConsortiumName?: string;
    CoverImageUrl?: string;
    Introduction?: string;
    Introduce?: string;
    Extend?: string;
    Address?: string;
    LatLon?: string;
    Phone?: string;
    CreatedTime?: string;
  }
}

export {};
