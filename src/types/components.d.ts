/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppLink: typeof import('./../components/AppLink/index.vue')['default']
    BaseChat: typeof import('./../components/BaseChat/index.vue')['default']
    BaseTableSearchContainer: typeof import('./../components/BaseTableSearchContainer/index.vue')['default']
    Breadcrumb: typeof import('./../components/Breadcrumb/index.vue')['default']
    CertificationContent: typeof import('./../components/CertificationContent/index.vue')['default']
    ChangePassword: typeof import('./../components/change-password/index.vue')['default']
    CityPicker: typeof import('./../components/CityPicker/index.vue')['default']
    CopyButton: typeof import('./../components/CopyButton/index.vue')['default']
    DeptSelect: typeof import('./../components/DeptSelect/index.vue')['default']
    DictLabel: typeof import('./../components/Dict/DictLabel.vue')['default']
    DragContainer: typeof import('./../components/DragContainer/index.vue')['default']
    EditTree: typeof import('./../components/EditTree/index.vue')['default']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBacktop: typeof import('element-plus/es')['ElBacktop']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElImageViewer: typeof import('element-plus/es')['ElImageViewer']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElInputTag: typeof import('element-plus/es')['ElInputTag']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSelectV2: typeof import('element-plus/es')['ElSelectV2']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTableV2: typeof import('element-plus/es')['ElTableV2']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTransfer: typeof import('element-plus/es')['ElTransfer']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    FileUpload: typeof import('./../components/Upload/FileUpload.vue')['default']
    FormItem: typeof import('./../components/FormItem/index.vue')['default']
    FormItemContainer: typeof import('./../components/FormItemContainer/index.vue')['default']
    Fullscreen: typeof import('./../components/Fullscreen/index.vue')['default']
    Hamburger: typeof import('./../components/Hamburger/index.vue')['default']
    HospitalSelect: typeof import('./../components/HospitalSelect/index.vue')['default']
    HospitalTransfer: typeof import('./../components/HospitalTransfer/index.vue')['default']
    IconSelect: typeof import('./../components/IconSelect/index.vue')['default']
    IdentityContent: typeof import('./../components/IdentityContent/index.vue')['default']
    KSelect: typeof import('./../components/KSelect/index.vue')['default']
    LangSelect: typeof import('./../components/LangSelect/index.vue')['default']
    LockScreen: typeof import('./../components/LockScreen/index.vue')['default']
    LogisticsInfo: typeof import('./../components/LogisticsInfo/index.vue')['default']
    MenuSearch: typeof import('./../components/MenuSearch/index.vue')['default']
    MessageCustom: typeof import('./../components/BaseChat/components/MessageCustom.vue')['default']
    MessageImage: typeof import('./../components/BaseChat/components/MessageImage.vue')['default']
    MessageList: typeof import('./../components/BaseChat/components/MessageList.vue')['default']
    MessageSystemText: typeof import('./../components/BaseChat/components/MessageSystemText.vue')['default']
    MessageText: typeof import('./../components/BaseChat/components/MessageText.vue')['default']
    MessageVideo: typeof import('./../components/BaseChat/components/MessageVideo.vue')['default']
    MultiImageUpload: typeof import('./../components/Upload/MultiImageUpload.vue')['default']
    MultipleCityPicker: typeof import('./../components/MultipleCityPicker/index.vue')['default']
    Pagination: typeof import('./../components/Pagination/index.vue')['default']
    PreApplyRecordAiResults: typeof import('./../components/PreApplyRecordAiResults/index.vue')['default']
    QRCode: typeof import('./../components/QRCode/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SingleFileUpload: typeof import('./../components/Upload/SingleFileUpload.vue')['default']
    SingleImageUpload: typeof import('./../components/Upload/SingleImageUpload.vue')['default']
    SizeSelect: typeof import('./../components/SizeSelect/index.vue')['default']
    SortTable: typeof import('./../components/SortTable/index.vue')['default']
    TableSelect: typeof import('./../components/TableSelect/index.vue')['default']
    TagsSelect: typeof import('./../components/TagsSelect/index.vue')['default']
    TBSearchContainer: typeof import('./../components/TBSearchContainer/index.vue')['default']
    UserSelect: typeof import('./../components/UserSelect/index.vue')['default']
    VerifySlider: typeof import('./../components/verify-slider/index.vue')['default']
    VScaleScreen: typeof import('./../components/VScaleScreen.vue')['default']
    WangEditor: typeof import('./../components/WangEditor/index.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
