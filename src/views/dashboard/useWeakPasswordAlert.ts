import { useAppStoreHook, useProtectionStoreHook } from "@/store";

export function useWeakPasswordAlert() {
  const isShowWeakPasswordDialog = ref(false);

  const appStore = useAppStoreHook();
  const protectionStore = useProtectionStoreHook();

  watchEffect(() => {
    if (appStore.dynamicConfig.protectionEnable && protectionStore.isWeak) {
      isShowWeakPasswordDialog.value = true;
    }
  });
  return {
    isShowWeakPasswordDialog,
  };
}
