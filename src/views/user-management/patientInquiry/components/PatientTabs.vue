<template>
  <div class="p-20px">
    <el-tabs type="card">
      <el-tab-pane label="基本信息">
        <BasicInfo :patient-id="props.patientId" />
      </el-tab-pane>
      <el-tab-pane label="病历档案">
        <MedicalRecordList :patient-id="props.patientId" :org-id="props.orgId" />
      </el-tab-pane>
    </el-tabs>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button type="primary" @click="emit('cancel')">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import BasicInfo from "./BasicInfo.vue";
import MedicalRecordList from "./MedicalRecordList.vue";

const props = defineProps<{
  patientId: string;
  orgId?: string;
}>();

const emit = defineEmits<{
  cancel: [];
}>();
</script>

<style lang="scss" scoped></style>
