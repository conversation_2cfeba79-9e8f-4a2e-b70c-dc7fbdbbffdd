<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="医院" prop="orgIds">
                <HospitalSelect
                  v-model="queryParams.orgIds"
                  :scopeable="true"
                  @change="onSwitchOrganization"
                />
              </el-form-item>
              <el-form-item label="科室" prop="DeptId">
                <DeptSelect
                  v-model="queryParams.departmentId"
                  :org-id="queryParams.orgIds"
                  :disabled="!queryParams.orgIds"
                  @change="
                    () => {
                      queryParams.doctorUserId = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="角色" prop="roleType">
                <KSelect
                  v-model="roleType"
                  :data="roleTypeList"
                  :props="{ label: 'Name', value: 'RoleType' }"
                  :loading="roleTypesLoading"
                  :show-all="true"
                  filterable
                  @change="
                    () => {
                      queryParams.doctorUserId = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="所属医生" prop="doctorUserId">
                <UserSelect
                  v-model="queryParams.doctorUserId"
                  :disabled="!roleType"
                  :scopeable="true"
                  :org-ids="queryParams.orgIds ? [queryParams.orgIds!] : null"
                  :dept-ids="queryParams.departmentId ? [queryParams.departmentId!] : null"
                  :role-types="roleType ? [roleType] : null"
                />
              </el-form-item>
              <el-form-item label="关键字" prop="keywords">
                <el-input
                  v-model="queryParams.keywords"
                  placeholder="患者姓名"
                  prefix-icon="Search"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="是否测试数据">
                <KSelect
                  v-model="queryParams.isTest"
                  :data="[
                    { label: '全部' },
                    { label: '否', value: '否' },
                    { label: '是', value: '是' },
                  ]"
                />
              </el-form-item>
              <el-form-item label="按标签筛选" prop="tagKeyWordList">
                <KSelect
                  v-model="queryParams.tagKeyWordList"
                  style="width: 300px"
                  :data="diseaseList"
                  :props="{ label: 'Key', value: 'Key' }"
                  :loading="diseaseLoading"
                  multiple
                  filterable
                  :disabled="!queryParams.orgIds"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button v-hasNoPermission="['externalSeller']" type="primary" @click="onExport">
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="Id"
          :height="tableFluidHeight"
          border
          highlight-current-row
        >
          <el-table-column prop="Name" label="姓名" width="100" align="center" />
          <el-table-column prop="Sex" label="性别" width="80" align="center" />
          <el-table-column prop="Age" label="年龄" width="80" align="center" />
          <el-table-column prop="Tags" label="患者标签" align="center" min-width="150" />
          <el-table-column prop="PhoneNumber" label="手机号" width="100" align="center" />
          <el-table-column prop="ConsultCount" label="问诊次数" width="80" align="center" />
          <el-table-column
            prop="LastConsultDate"
            label="上次问诊时间"
            :formatter="tableDateFormat"
            width="150"
            align="center"
          />
          <el-table-column prop="OrganizationName" label="医院" width="120" align="center" />
          <el-table-column prop="TrainingCount" label="康复计划数" width="80" align="center" />
          <el-table-column
            prop="ExecProgramState"
            label="是否正在执行康复计划"
            width="80"
            align="center"
          />
          <el-table-column prop="BinDingDoc" label="所属医生" align="center" min-width="150">
            <template #default="scope">
              {{ deduplicationData(scope.row.BinDingDoc) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="BinDingTherapist"
            label="所属治疗师"
            align="center"
            min-width="150"
          >
            <template #default="scope">
              {{ deduplicationData(scope.row.BinDingTherapist) }}
            </template>
          </el-table-column>
          <el-table-column prop="BinDingNurse" label="所属护士" align="center" min-width="150">
            <template #default="scope">
              {{ deduplicationData(scope.row.BinDingNurse) }}
            </template>
          </el-table-column>
          <el-table-column label="是否测试数据" align="center" prop="IsTest" width="80" />
          <el-table-column fixed="right" label="操作" width="100" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="onPreviewPatient(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 查看患者信息标签页 -->
  <el-dialog
    v-model="showTabsDialog.isShow"
    :title="showTabsDialog.title"
    width="900"
    destroy-on-close
    @close="showTabsDialog.isShow = false"
  >
    <PatientTabs
      :patient-id="showTabsDialog.data"
      :org-id="queryParams.orgIds"
      @cancel="showTabsDialog.isShow = false"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import Report_Api from "@/api/report";
import { convertToRedashParams, exportExcel, getExportCols } from "@/utils/serviceUtils";
import { useUserStore } from "@/store";
import Passport_Api from "@/api/passport";
import Dictionary_Api from "@/api/dictionary";
import { ExportTaskRedashDTO, PatientInquiryParams, PatientRedash } from "@/api/report/types";
import { ExportEnum } from "@/enums/Other";
import PatientTabs from "./components/PatientTabs.vue";

interface QueryParams extends RedashParameters<PatientInquiryParams> {
  tagKeyWordList: string[];
}

// 调试开关
const kEnableDebug = false;
defineOptions({
  name: "PatientInquiry",
});

const {
  kTableRef,
  tableRef,
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  tableDateFormat,
  exportLoading,
} = useTableConfig<PatientRedash>();
const userStore = useUserStore();

// 查询结果ID，用于导出功能
let queryResultId = -1;

// 查询条件
const queryParams = reactive<QueryParams>({
  LoginUserId: userStore.userInfo.Id,
  departmentId: undefined,
  doctorUserId: undefined,
  isTest: undefined,
  keywords: undefined,
  orgIds: undefined,
  tagKeyWords: ".",
  tagKeyWordList: [],
  pageIndex: 1,
  pageSize: 20,
});

watch(
  () => queryParams.tagKeyWordList,
  () => {
    kEnableDebug && console.debug("tagKeyWordList", queryParams.tagKeyWordList);

    if (queryParams.tagKeyWordList.length === 0) {
      queryParams.tagKeyWords = ".";
    } else {
      queryParams.tagKeyWords = queryParams.tagKeyWordList.join("|");
    }
  }
);

// 角色列表
const roleTypeList = ref<BaseRole[]>([]);
const roleTypesLoading = ref(false);
const roleType = ref<string>();

// 疾病列表
const diseaseList = ref<DictCode[]>([]);
const diseaseLoading = ref(false);

// 查看弹窗
const showTabsDialog = reactive({
  isShow: false,
  title: "",
  data: "",
});

// 点击搜索
function handleQuery() {
  queryParams.pageIndex = 1;
  requestTableList();
}

// 点击导出
async function onExport() {
  kEnableDebug && console.debug("点击导出");

  const { tagKeyWordList, ...parameters } = queryParams;
  const exportParams = convertToRedashParams(parameters, "Report_PatientsDetail");
  const params: ExportTaskRedashDTO = {
    Cols: getExportCols(tableRef.value!.columns as any, "@"),
    ExecutingParams: exportParams.parameters,
    ExportWay: ExportEnum.PlainMySql,
    FileName: `患者查询-${Date.now()}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.pageIndex,
    PageSize: queryParams.pageSize,
    QueryName: "Report_PatientsDetail",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
}

// 切换医院
function onSwitchOrganization() {
  kEnableDebug && console.debug("切换医院");

  queryParams.departmentId = undefined;
  queryParams.doctorUserId = undefined;
  queryParams.tagKeyWords = ".";
  roleType.value = undefined;
  requestDiseaseList();
}

// 点击查看
async function onPreviewPatient(row?: PatientRedash) {
  kEnableDebug && console.debug("查看", row);
  if (!row?.UserId) {
    ElMessage.error("患者id为空");
    return;
  }

  showTabsDialog.title = "查看";
  showTabsDialog.data = row.UserId;
  showTabsDialog.isShow = true;
}

// 数据去重
function deduplicationData(data: string) {
  const arr = data.split(",");
  const set = new Set(arr);
  return Array.from(set).join(",");
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const { tagKeyWordList, ...parameters } = queryParams;

  const params = convertToRedashParams(parameters, "Report_PatientsDetail");
  const r = await Report_Api.getRedashList<PatientRedash>(params);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  queryResultId = r.Data.QueryResultId;
  pageData.value = r.Data.Data;
  total.value = r.Data.TotalCount;
}

// 请求角色类型列表
async function requestRoleTypeList() {
  roleTypesLoading.value = true;
  const r = await Passport_Api.read({});
  roleTypesLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  roleTypeList.value = r.Data.filter(
    (item) =>
      item.RoleType === "doctor" || item.RoleType === "therapist" || item.RoleType === "nurse"
  );
}

// 获取疾病列表
async function requestDiseaseList() {
  diseaseLoading.value = true;
  const params: DictQueryParams = {
    PageCondition: {
      PageIndex: 1,
      PageSize: 99,
      SortConditions: [
        {
          SortField: "Id",
          ListSortDirection: 1,
        },
      ],
    },
    FilterGroup: {
      Rules: [],
      Groups: [
        {
          Rules: [
            {
              Field: "Code",
              Value: "DiseaseTag",
              Operate: 11,
            },
          ],
          Operate: 2,
        },
      ],
      Operate: 1,
    },
  };
  const r = await Dictionary_Api.readStd(params);
  if (r.Type !== 200 || !r.Data.Rows.length) {
    diseaseLoading.value = false;
    ElMessage.error(r.Content);
    return;
  }

  const data: DictQueryParams = {
    PageCondition: {
      PageIndex: 1,
      PageSize: 100,
      SortConditions: [
        {
          SortField: "OrderNumber",
          ListSortDirection: 1,
        },
      ],
    },
    FilterGroup: {
      Rules: [
        ...(r.Data.Rows[0].Id
          ? [
              {
                Field: "DictId",
                Value: r.Data.Rows[0].Id,
                Operate: 3,
              },
            ]
          : []),
        ...(queryParams.orgIds
          ? [
              {
                Field: "OrgId",
                Value: queryParams.orgIds,
                Operate: 3,
              },
            ]
          : []),
      ],
      Operate: 1,
    },
  };
  const r2 = await Dictionary_Api.readDict(data);
  diseaseLoading.value = false;
  if (r2.Type !== 200) {
    ElMessage.error(r2.Content);
    return;
  }

  diseaseList.value = r2.Data.Rows;
}

onMounted(() => {
  requestRoleTypeList();
});

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
