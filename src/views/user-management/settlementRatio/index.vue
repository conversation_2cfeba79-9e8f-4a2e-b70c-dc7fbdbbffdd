<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="角色" prop="RoleName">
                <el-select
                  v-model="queryParams.RoleName"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  class="w-80px!"
                >
                  <el-option label="医生" value="医生" />
                  <el-option label="治疗师" value="治疗师" />
                  <el-option label="护士" value="护士" />
                </el-select>
              </el-form-item>
              <el-form-item label="机构" prop="OrgIds">
                <HospitalSelect v-model="queryParams.OrgIds" @change="handleOrgChange" />
              </el-form-item>
              <el-form-item label="科室" prop="DeptId">
                <DeptSelect
                  v-model="queryParams.DeptId"
                  :org-id="queryParams.OrgIds ? queryParams.OrgIds : null"
                  :disabled="!queryParams.OrgIds"
                />
              </el-form-item>
              <el-form-item label="绑定科主任">
                <UserSelect
                  v-model="queryParams.DeptChiefId"
                  placeholder="请选择绑定科主任"
                  :org-ids="queryParams.OrgIds ? [queryParams.OrgIds] : null"
                  :dept-ids="queryParams.DeptId ? [queryParams.DeptId] : null"
                  :role-types="null"
                  :scopeable="false"
                />
              </el-form-item>
              <el-form-item label="绑定院长">
                <UserSelect
                  v-model="queryParams.PresidentId"
                  placeholder="请选择绑定院长"
                  :org-ids="queryParams.OrgIds ? [queryParams.OrgIds] : null"
                  :dept-ids="queryParams.DeptId ? [queryParams.DeptId] : null"
                  :role-types="null"
                  :scopeable="false"
                />
              </el-form-item>
              <el-form-item label="绑定护士长">
                <UserSelect
                  v-model="queryParams.NurseChiefId"
                  placeholder="请选择绑定护士长"
                  :org-ids="queryParams.OrgIds ? [queryParams.OrgIds] : null"
                  :dept-ids="queryParams.DeptId ? [queryParams.DeptId] : null"
                  :role-types="null"
                  :scopeable="false"
                />
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="姓名/电话号码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="handleEdit(null, 1)">批量编辑结算比例</el-button>
            <el-button type="primary" @click="handleEdit(null, 2)">批量编辑绑定</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="UserId"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
          @select="handleTableSelect"
          @select-all="handleTableSelect"
        >
          <el-table-column align="center" type="selection" reserve-selection width="55" />
          <el-table-column label="角色" width="80" align="center">
            <template #default="scope">
              {{ scope.row.RoleName }}
            </template>
          </el-table-column>
          <el-table-column label="姓名" align="center">
            <template #default="scope">
              {{ scope.row.Name }}
            </template>
          </el-table-column>
          <el-table-column label="手机号" width="150" align="center">
            <template #default="scope">
              {{ scope.row.PhoneNumber }}
            </template>
          </el-table-column>
          <el-table-column label="入驻机构" align="center">
            <template #default="scope">
              {{ scope.row.OrgName }}
            </template>
          </el-table-column>
          <el-table-column label="科室" align="center">
            <template #default="scope">
              {{ scope.row.DeptName }}
            </template>
          </el-table-column>
          <el-table-column label="绑定科主任" width="80" align="center">
            <template #default="scope">
              {{ scope.row.BindDeptChiefName }}
            </template>
          </el-table-column>
          <el-table-column label="绑定院长" width="80" align="center">
            <template #default="scope">
              {{ scope.row.BindPresidentName }}
            </template>
          </el-table-column>
          <el-table-column label="绑定护士长" width="80" align="center">
            <template #default="scope">
              {{ scope.row.BindNurseChiefName }}
            </template>
          </el-table-column>
          <el-table-column label="开方结算比例1" width="80" align="center">
            <template #default="scope">
              {{ scope.row.PrescriptionSettleRatio1 }}
            </template>
          </el-table-column>
          <el-table-column label="开方结算比例2" width="80" align="center">
            <template #default="scope">
              {{ scope.row.PrescriptionSettleRatio2 }}
            </template>
          </el-table-column>
          <el-table-column label="指导结算比例" width="80" align="center">
            <template #default="scope">
              {{ scope.row.GuideSettleRatio }}
            </template>
          </el-table-column>
          <el-table-column label="管理结算比例（科主任）" width="80" align="center">
            <template #default="scope">
              {{ scope.row.DeptChiefSettleRatio }}
            </template>
          </el-table-column>
          <el-table-column label="管理结算比例（院长）" width="80" align="center">
            <template #default="scope">
              {{ scope.row.PresidentSettleRatio }}
            </template>
          </el-table-column>
          <el-table-column label="管理结算比例（护士长）" width="80" align="center">
            <template #default="scope">
              {{ scope.row.NurseChiefSettleRatio }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="80">
            <template #default="scope">
              <!--  v-hasPermission="['finance']" -->
              <el-button link type="primary" size="small" @click="handleEdit(scope.row, 0)">
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog
      v-model="showEditDialog"
      title="编辑"
      width="550"
      destroy-on-close
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <SettlementContent
        ref="settlementContentRef"
        :settlement-info="settlementInfo"
        :mode="updateMode"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handleUpdate">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import Report_Api from "@/api/report";
import { SettlementInfoInputDTO, SettlementInfoItem } from "@/api/report/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import { convertToRedashParams } from "@/utils/serviceUtils";
import SettlementContent from "./components/SettlementContent.vue";
import Consult_Api from "@/api/consult";

defineOptions({
  name: "SettlementRatio",
});

const settlementInfo = ref<SettlementInfoItem | null>(null);
const settlementContentRef = useTemplateRef("settlementContentRef");

const queryParams = ref<SettlementInfoInputDTO>({
  RoleName: null,
  OrgIds: null,
  DeptId: null,
  DeptChiefId: null,
  PresidentId: null,
  NurseChiefId: null,
  Keyword: "",
  UserId: "*",
  PageIndex: 1,
  PageSize: 20,
});

const showEditDialog = ref<boolean>(false);
const dialogConfirmLoading = ref<boolean>(false);
const updateMode = ref<number>(0);

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize, selectedTableIds } =
  useTableConfig<SettlementInfoItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handleOrgChange = () => {
  selectedTableIds.value = [];
  tableRef.value?.clearSelection();
  queryParams.value.DeptId = null;
  queryParams.value.DeptChiefId = null;
  queryParams.value.PresidentId = null;
  queryParams.value.NurseChiefId = null;
};

const handleTableSelect = (selection: SettlementInfoItem[]) => {
  selectedTableIds.value = selection.map((item) => item.UserId!);
};

const handleUpdate = () => {
  dialogConfirmLoading.value = true;
  const params = settlementContentRef.value?.handleSubmit();
  let settlements: any[] = [];
  selectedTableIds.value.forEach((id) => {
    settlements.push({
      ...params,
      UserId: id,
    });
  });
  Consult_Api.setSettlements({
    Mode: updateMode.value,
    Settlements: settlements,
  })
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success(res.Content!);
        showEditDialog.value = false;
        selectedTableIds.value = [];
        tableRef.value?.clearSelection();
        handleGetTableList();
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

const handleEdit = (row: SettlementInfoItem | null, mode: number) => {
  if (mode !== 0 && !queryParams.value.OrgIds) {
    ElMessage.warning("请先选择机构");
    return;
  }
  if (mode !== 0 && !selectedTableIds.value.length) {
    ElMessage.warning("请先选择要需要编辑的医生/治疗师/护士");
    return;
  }
  if (row) {
    selectedTableIds.value = [row.UserId!];
    tableRef.value?.clearSelection();
    tableRef.value?.toggleRowSelection(row, true);
  }
  settlementInfo.value = row;
  updateMode.value = mode;
  showEditDialog.value = true;
};
const handleGetTableList = async () => {
  const params = convertToRedashParams(queryParams.value, "Report_SettlementInfo");
  tableLoading.value = true;
  const res = await Report_Api.getRedashList<SettlementInfoItem>(params);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
