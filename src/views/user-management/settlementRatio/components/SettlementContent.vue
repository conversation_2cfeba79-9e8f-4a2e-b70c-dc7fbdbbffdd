<template>
  <el-tabs v-model="activeName" type="card">
    <el-tab-pane v-if="mode !== 2" label="结算比例" name="结算比例">
      <FormItem required label="开方结算比例1">
        <el-input-number
          v-model="info.PrescriptionSettleRatio1"
          :min="0"
          :step="0.01"
          step-strictly
          :max="1"
          label="开方结算比例1"
          style="width: 150px"
        />
        <span style="color: gray; font-size: 11px">（下达方案结算比例）</span>
      </FormItem>
      <FormItem required label="开方结算比例2">
        <el-input-number
          v-model="info.PrescriptionSettleRatio2"
          :min="0"
          :step="0.01"
          step-strictly
          :max="1"
          label="开方结算比例2"
          style="width: 150px"
        />
        <span style="color: gray; font-size: 11px">（单独下方，无指导人时结算比例）</span>
      </FormItem>
      <FormItem required label="指导结算比例">
        <el-input-number
          v-model="info.GuideSettleRatio"
          :min="0"
          :step="0.01"
          step-strictly
          :max="1"
          label="指导结算比例"
          style="width: 150px"
        />
        <span style="color: gray; font-size: 11px">（被指派时结算比例）</span>
      </FormItem>
      <FormItem label="管理结算比例(科主任)">
        <el-input-number
          v-model="info.DeptChiefSettleRatio"
          :min="0"
          :step="0.01"
          step-strictly
          :max="1"
          label="管理结算比例(科主任)"
          style="width: 150px"
        />
        <span style="color: gray; font-size: 11px">（医疗质量监管和技术指导,结算比例）</span>
      </FormItem>
      <FormItem label="管理结算比例(院长)">
        <el-input-number
          v-model="info.PresidentSettleRatio"
          :min="0"
          :step="0.01"
          step-strictly
          :max="1"
          label="管理结算比例(院长)"
          style="width: 150px"
        />
        <span style="color: gray; font-size: 11px">（医疗质量监管及科室管理，结算比例）</span>
      </FormItem>
      <FormItem label="管理结算比例(护士长)">
        <el-input-number
          v-model="info.NurseChiefSettleRatio"
          :min="0"
          :step="0.01"
          step-strictly
          :max="1"
          label="管理结算比例(护士长)"
          style="width: 150px"
        />
        <span style="color: gray; font-size: 11px">（管理管床护士,结算比例）</span>
      </FormItem>
    </el-tab-pane>
    <el-tab-pane v-if="mode !== 1" label="绑定" name="绑定">
      <FormItem label="医疗质量监管(科主任)">
        <UserSelect
          v-model="info.BindDeptChiefId"
          :role-types="['doctor', 'nurse', 'therapist']"
          :default-options="
            info.BindDeptChiefId && info.BindDeptChiefName
              ? [{ Id: info.BindDeptChiefId, Name: info.BindDeptChiefName }]
              : []
          "
        />
      </FormItem>
      <FormItem label="医疗质量监管(院长)">
        <UserSelect
          v-model="info.BindPresidentId"
          :role-types="['doctor', 'nurse', 'therapist']"
          :default-options="
            info.BindPresidentId && info.BindPresidentName
              ? [{ Id: info.BindPresidentId, Name: info.BindPresidentName }]
              : []
          "
        />
      </FormItem>
      <FormItem label="医疗质量监管(护士长)">
        <UserSelect
          v-model="info.BindNurseChiefId"
          :role-types="['nurse']"
          :default-options="
            info.BindNurseChiefId && info.BindNurseChiefName
              ? [{ Id: info.BindNurseChiefId, Name: info.BindNurseChiefName }]
              : []
          "
        />
      </FormItem>
    </el-tab-pane>
    <el-tab-pane v-if="mode !== 1" label="其他绑定" name="其他绑定">
      <div class="flex">
        <FormItem label="市场开发">
          <UserSelect
            v-model="info.MarketingId"
            :role-types="null"
            :default-options="
              info.MarketingId && info.MarketingName
                ? [{ Id: info.MarketingId, Name: info.MarketingName }]
                : []
            "
          />
        </FormItem>
        <FormItem label="结算比例">
          <el-input-number
            v-model="info.MarketingSettleRatio"
            style="width: 120px"
            :min="0"
            :precision="2"
            :step="0.01"
            :max="1"
            step-strictly
          />
        </FormItem>
      </div>
      <div class="flex">
        <FormItem label="医助管理">
          <UserSelect
            v-model="info.AssistantManagerId"
            :role-types="null"
            :default-options="
              info.AssistantManagerId && info.AssistantManagerName
                ? [{ Id: info.AssistantManagerId, Name: info.AssistantManagerName }]
                : []
            "
          />
        </FormItem>
        <FormItem label="结算比例">
          <el-input-number
            v-model="info.AssistantManagerSettleRatio"
            style="width: 120px"
            :min="0"
            :precision="2"
            :step="0.01"
            :max="1"
            step-strictly
          />
        </FormItem>
      </div>
      <div class="flex">
        <FormItem label="上线人员">
          <UserSelect
            v-model="info.OnlineGuidanceId"
            :role-types="null"
            :default-options="
              info.OnlineGuidanceId && info.OnlineGuidanceName
                ? [{ Id: info.OnlineGuidanceId, Name: info.OnlineGuidanceName }]
                : []
            "
          />
        </FormItem>
        <FormItem label="结算比例">
          <el-input-number
            v-model="info.OnlineGuidanceSettleRatio"
            style="width: 120px"
            :min="0"
            :precision="2"
            :step="0.01"
            :max="1"
            step-strictly
          />
        </FormItem>
      </div>
      <div class="flex">
        <FormItem label="经销商">
          <UserSelect
            v-model="info.DealerId"
            :role-types="null"
            :default-options="
              info.DealerId && info.DealerName ? [{ Id: info.DealerId, Name: info.DealerName }] : []
            "
          />
        </FormItem>
        <FormItem label="结算比例">
          <el-input-number
            v-model="info.DealerSettleRatio"
            style="width: 120px"
            :min="0"
            :precision="2"
            :step="0.01"
            :max="1"
            step-strictly
          />
        </FormItem>
      </div>
      <div class="flex">
        <FormItem label="推荐人">
          <UserSelect
            v-model="info.ReferrerId"
            :role-types="null"
            :default-options="
              info.ReferrerId && info.ReferrerName
                ? [{ Id: info.ReferrerId, Name: info.ReferrerName }]
                : []
            "
          />
        </FormItem>
        <FormItem label="结算比例">
          <el-input-number
            v-model="info.ReferrerSettleRatio"
            style="width: 120px"
            :min="0"
            :precision="2"
            :step="0.01"
            :max="1"
            step-strictly
          />
        </FormItem>
      </div>
      <div class="flex">
        <FormItem label="其他">
          <UserSelect
            v-model="info.OtherId"
            :role-types="null"
            :default-options="
              info.OtherId && info.OtherName ? [{ Id: info.OtherId, Name: info.OtherName }] : []
            "
          />
        </FormItem>
        <FormItem label="结算比例">
          <el-input-number
            v-model="info.OtherSettleRatio"
            style="width: 120px"
            :min="0"
            :precision="2"
            :step="0.01"
            :max="1"
            step-strictly
          />
        </FormItem>
      </div>
      <div class="flex">
        <FormItem label="医助结算比例">
          <el-input-number
            v-model="info.AssistantSettleRatio"
            style="width: 120px"
            :min="0"
            :precision="2"
            :step="0.01"
            :max="1"
            step-strictly
          />
        </FormItem>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import { SettlementInfoItem } from "@/api/report/types";

interface PageSettlementInfoItem extends Required<SettlementInfoItem> {}

const info = ref<PageSettlementInfoItem>({
  OrgId: "",
  UserId: "",
  RoleName: "",
  Name: "",
  PhoneNumber: "",
  OrgName: "",
  DeptName: "",
  BindDeptChiefName: "",
  BindPresidentName: "",
  BindNurseChiefName: "",
  PrescriptionSettleRatio1: null,
  PrescriptionSettleRatio2: null,
  GuideSettleRatio: null,
  DeptChiefSettleRatio: null,
  PresidentSettleRatio: null,
  NurseChiefSettleRatio: null,
  MarketingName: "",
  MarketingSettleRatio: null,
  AssistantManagerName: "",
  AssistantManagerSettleRatio: null,
  OnlineGuidanceName: "",
  OnlineGuidanceSettleRatio: null,
  DealerName: "",
  DealerSettleRatio: null,
  ReferrerName: "",
  ReferrerSettleRatio: null,
  OtherName: "",
  OtherSettleRatio: null,
  AssistantName: "",
  AssistantSettleRatio: null,
  BindDeptChiefId: "",
  BindPresidentId: "",
  BindNurseChiefId: "",
  MarketingId: "",
  AssistantManagerId: "",
  OnlineGuidanceId: "",
  DealerId: "",
  ReferrerId: "",
  OtherId: "",
  AssistantId: "",
});
const handleSubmit = (): PageSettlementInfoItem => {
  const copyData = JSON.parse(JSON.stringify(info.value));
  Object.keys(copyData).forEach((key) => {
    if (key.includes("Ratio") && !copyData[key as keyof SettlementInfoItem]) {
      copyData[key as keyof SettlementInfoItem] = 0;
    }
  });
  return copyData as PageSettlementInfoItem;
};

const onProcessInfoToTabs = (settlementInfo: SettlementInfoItem) => {
  const copyData: SettlementInfoItem = JSON.parse(JSON.stringify(settlementInfo));
  Object.keys(copyData).forEach((key) => {
    if (key.includes("Ratio")) {
      copyData[key as keyof SettlementInfoItem] = copyData[key as keyof SettlementInfoItem]
        ? Number(copyData[key as keyof SettlementInfoItem])
        : null;
    }
  });
  info.value = copyData as PageSettlementInfoItem;
};

interface Props {
  settlementInfo: SettlementInfoItem | null;
  mode: number;
}
const props = defineProps<Props>();
const activeName = ref<string>(props.mode === 2 ? "绑定" : "结算比例");
watch(
  () => props.settlementInfo,
  (newVal) => {
    if (!newVal) {
      return;
    }
    // 获取用户的所有信息
    onProcessInfoToTabs(newVal);
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>
