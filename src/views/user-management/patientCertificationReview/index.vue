<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="审核状态">
                <el-select
                  v-model="queryParams.State"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  value-on-clear="99"
                >
                  <el-option v-show="false" label="全部" value="99" />
                  <el-option label="待审核" :value="PatientCertificationState.PendingReview" />
                  <el-option label="已通过" :value="PatientCertificationState.Approved" />
                  <el-option label="已拒绝" :value="PatientCertificationState.Rejected" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="姓名/昵称"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="UserId"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="Name" label="姓名" align="center" />
          <el-table-column prop="Sex" label="性别" align="center" />
          <el-table-column prop="PhoneNumber" label="手机号" align="center" />
          <el-table-column prop="UserName" label="用户名" width="120" align="center" />
          <el-table-column prop="NickName" label="昵称" align="center" />
          <el-table-column prop="WorkflowStatus" label="审核状态" align="center">
            <template #default="scope">
              <span>{{ APPROVE_STATUS[scope.row.WorkflowStatus] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            prop="CreatedTime"
            :formatter="tableDateFormat"
            align="center"
          />
          <el-table-column fixed="right" label="操作" align="center">
            <template #default="scope">
              <div class="text-center">
                <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, true)">
                  查看
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageInput.PageIndex"
          v-model:limit="queryParams.PageInput.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog v-model="showPatientContent" title="查看" width="900px" destroy-on-close>
      <PatientCertificationContent
        ref="patientCertificationContentRef"
        :patient-certification-info="patientCertificationInfo"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showPatientContent = false">取消</el-button>
          <el-button v-if="!isPreview" type="primary" :loading="dialogConfirmLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import { PatientCertificationState } from "@/enums/UserEnum";
import Passport_Api from "@/api/passport";
import { GetAuthUsersInputDTO, PatientCertificationItem } from "@/api/passport/types";
import PatientCertificationContent from "./components/PatientCertificationContent.vue";

defineOptions({
  name: "PatientCertificationReview",
});

const queryParams = ref<GetAuthUsersInputDTO>({
  State: "99",
  Keyword: "",
  PageInput: {
    PageIndex: 1,
    PageSize: 20,
  },
});

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);
const APPROVE_STATUS = ["待审核", "已暂停", "已通过", "已拒绝"];
const showPatientContent = ref<boolean>(false);
const dialogConfirmLoading = ref<boolean>(false);
const patientCertificationInfo = ref<PatientCertificationItem | null>(null);

const { tableLoading, pageData, total, tableRef, tableDateFormat, tableFluidHeight, tableResize } =
  useTableConfig<unknown>();

const handleQuery = () => {
  queryParams.value.PageInput.PageIndex = 1;
  handleGetTableList();
};

const handlePreviewOrEdit = async (row: PatientCertificationItem, isPreviewState: boolean) => {
  isPreview.value = isPreviewState;
  patientCertificationInfo.value = row;
  showPatientContent.value = true;
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Passport_Api.getAuthUsers(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Rows;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
