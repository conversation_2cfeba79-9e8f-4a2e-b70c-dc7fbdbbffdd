<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="前端医助">
                <UserSelect
                  v-model="queryParams.OrgFrontendAssistantId"
                  :role-types="['assistant']"
                  dto-type-name="QueryUserOutputDto3"
                  class="w-150px!"
                />
              </el-form-item>
              <el-form-item label="后端医助">
                <UserSelect
                  v-model="queryParams.OrgBackendAssistantId"
                  :role-types="['assistant']"
                  dto-type-name="QueryUserOutputDto3"
                  class="w-150px!"
                />
              </el-form-item>
              <el-form-item label="是否启用">
                <el-select
                  v-model="queryParams.IsEnabled"
                  placeholder="请选择"
                  clearable
                  style="width: 100px"
                  :empty-values="[null, undefined]"
                  :value-on-clear="() => null"
                >
                  <el-option label="启用" :value="true" />
                  <el-option label="禁用" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="医院名称/医院编码/医院地址"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="handlePreviewOrEdit(null, false)">新增</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="UserId"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column label="医院名称" prop="Name" align="center" width="180" />
          <el-table-column label="医院等级" align="center">
            <template #default="scope">
              {{ handleGetHosLevelName(scope.row.Level) }}
            </template>
          </el-table-column>
          <el-table-column prop="Category" label="医院类型" align="center">
            <template #default="scope">
              <span v-if="scope.row.Category !== null">
                {{ hosTypeList[scope.row.Category] }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="前端医助" align="center" width="140">
            <template #default="scope">
              <span v-if="scope.row.OrganizationPersonnel?.FrontendAssistantName">
                {{ scope.row.OrganizationPersonnel.FrontendAssistantName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="后端医助" align="center" width="140">
            <template #default="scope">
              <span v-if="scope.row.OrganizationPersonnel?.BackendAssistantName">
                {{ scope.row.OrganizationPersonnel.BackendAssistantName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="管理员账号" prop="AdminPhone" align="center" width="120" />
          <el-table-column label="是否治疗点" prop="IsTreatment" align="center" width="100">
            <template #default="scope">
              <span>{{ scope.row.IsTreatment ? "是" : "否" }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="PinyinCode" label="拼音码" align="center" />
          <el-table-column prop="Address" label="医院地址" align="center" width="160" />
          <el-table-column prop="Phone" label="医院电话" align="center" width="140" />
          <el-table-column prop="LatLon" label="医院经纬度" align="center" width="120" />
          <el-table-column prop="SignAContractTime" label="签约时间" align="center" width="180" />
          <el-table-column label="是否启用" align="center">
            <template #default="scope">
              <span>{{ scope.row.IsEnabled ? "是" : "否" }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="150" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, true)">
                查看
              </el-button>
              <el-button
                v-hasNoPermission="['promoter']"
                link
                type="primary"
                @click="handlePreviewOrEdit(scope.row, false)"
              >
                编辑
              </el-button>
              <el-button type="primary" link @click="handleSeeQrCode(scope.row)">
                查看二维码
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex!"
          v-model:limit="queryParams.PageSize!"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog
      v-model="dialogVisible.hospitalContent"
      :title="hospitalContentTitle"
      width="700"
      destroy-on-close
    >
      <HospitalContent ref="hospitalContentRef" :hospital-info="hospitalInfo" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.hospitalContent = false">取消</el-button>
          <el-button
            v-if="!isPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleSubmitHospitalInfo"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible.qrCode" :title="hospitalContentTitle" width="40%">
      <div class="qrcode-container">
        <el-card class="qrcode-card" shadow="hover">
          <template #header>
            <div class="qrcode-title">跳转医生列表</div>
          </template>
          <el-image
            v-if="orgImageUrls.isServer"
            class="qrcode-image"
            :src="orgImageUrls.ToDoctor"
          />
        </el-card>
        <el-card class="qrcode-card" shadow="hover">
          <template #header>
            <div class="qrcode-title">跳转医院首页</div>
          </template>
          <el-image
            v-if="orgImageUrls.isServer"
            class="qrcode-image"
            :src="orgImageUrls.ToHomePage"
          />
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import Dictionary_Api from "@/api/dictionary";
import { OrganizationListInputDTO } from "@/api/passport/types";
import Passport_Api from "@/api/passport";
import HospitalContent from "./components/HospitalContent.vue";
const hospitalContentRef = useTemplateRef("hospitalContentRef");
const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

defineOptions({
  name: "HospitalManagement",
  inheritAttrs: false,
});

interface DialogVisible {
  hospitalContent: boolean;
  qrCode: boolean;
}

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<BaseOrganization>();

const queryParams = ref<OrganizationListInputDTO>({
  OrgFrontendAssistantId: null,
  OrgBackendAssistantId: null,
  IsEnabled: null,
  Keyword: "",
  PageIndex: 1,
  PageSize: 20,
  Pageable: true,
  DtoTypeName: "QueryOrgDetailOutputDto1",
});
let hosLevelList = ref<ReadDict[]>([]);
const hosTypeList: string[] = ["普通医院", "社区医院", "诊所", "其他"];
provide("hosLevelList", hosLevelList);
provide("hosTypeList", hosTypeList);
const dialogVisible = ref<DialogVisible>({
  hospitalContent: false,
  qrCode: false,
});
const hospitalContentTitle = ref<string>("");
const dialogConfirmLoading = ref<boolean>(false);
const hospitalInfo = ref<BaseOrganization | null>(null);
const orgImageUrls = ref<{
  ToDoctor: string;
  ToHomePage: string;
  isServer: boolean;
}>({
  ToDoctor: "",
  ToHomePage: "",
  isServer: true,
});
const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handlePreviewOrEdit = async (row: BaseOrganization | null, isPreviewState: boolean) => {
  isPreview.value = row ? isPreviewState : false;
  hospitalContentTitle.value = row
    ? isPreviewState
      ? `查看${row?.Name}医院`
      : `编辑${row?.Name}医院`
    : "新增医院";
  if (row?.Id) {
    await handleGetHosDetails(row.Id);
  } else {
    hospitalInfo.value = null;
  }
  dialogVisible.value.hospitalContent = true;
};
const handleGetHosDetails = async (id: string) => {
  const res = await Passport_Api.getOrganizationById({ id });
  if (res.Type === 200) {
    hospitalInfo.value = res.Data;
  } else {
    hospitalInfo.value = null;
  }
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Passport_Api.getOrganizationList(queryParams.value);
  if (res.Type === 200) {
    res.Data.Rows.forEach((item) => {
      item.SignAContractTime = item.SignAContractTime
        ? dayjs(item.SignAContractTime).format("YYYY-MM-DD HH:mm:ss")
        : "";
    });
    pageData.value = res.Data.Rows || [];
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};
const handleGetHosLevelList = async () => {
  const res = await Dictionary_Api.getDict({ code: "HospitalLevel" });
  if (res.Type === 200) {
    res.Data.sort((a, b) => {
      return Number(a.Key) - Number(b.Key);
    });
    hosLevelList.value = res.Data;
  }
};
const handleGetHosLevelName = (key: number) => {
  if (key >= 0 && key != null && hosLevelList.value.length > 0) {
    const level = hosLevelList.value.find((v) => v.Key === key.toString());
    return level?.Value;
  }
  return "";
};
const handleSubmitHospitalInfo = async () => {
  const data = await hospitalContentRef.value?.handleSubmitInfo();
  const params = JSON.parse(JSON.stringify(data));
  if (!params) return;
  console.log("params", params);
  let debug = true;
  if (debug) {
  }
  const fun = params.Id ? Passport_Api.updateOrganization : Passport_Api.createOrganization;
  dialogConfirmLoading.value = true;
  fun([params])
    .then((res) => {
      if (res.Type === 200) {
        ElNotification.success(res.Content);
        dialogVisible.value.hospitalContent = false;
        handleGetTableList();
      } else {
        ElNotification.error(res.Content);
      }
    })
    .catch((err) => {
      ElNotification.error(JSON.stringify(err));
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

const handleSeeQrCode = (row: BaseOrganization) => {
  hospitalContentTitle.value = `${row.Name}医院二维码`;
  if (!row.QrCode) {
    ElNotification.error("还没有医院二维码,请编辑保存才会生成二维码");
    return;
  }
  const orgUrls = JSON.parse(row.QrCode);
  orgImageUrls.value = orgUrls;
  orgImageUrls.value.isServer = true;
  dialogVisible.value.qrCode = true;
};

onMounted(() => {
  // 获取医院等级
  handleGetHosLevelList();
});
onActivated(() => {
  handleGetTableList();
});
</script>

<style scoped lang="scss">
.qrcode-container {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  padding: 10px;
}

.qrcode-card {
  flex: 1;
  text-align: center;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-5px);
  }
}

.qrcode-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 0;
  text-align: center;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    transform: scale(1.02);
  }
}

.no-qrcode {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
}
</style>
