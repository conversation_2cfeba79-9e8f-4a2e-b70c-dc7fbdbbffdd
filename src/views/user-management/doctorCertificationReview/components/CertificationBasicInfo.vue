<template>
  <div class="overflow-y-auto h-500px">
    <el-row justify="space-between" class="mb-20px">
      <div class="flex">
        <el-text>头像：</el-text>
        <el-image style="width: 100px; height: 100px" :src="headImg" fit="cover" />
      </div>
      <div class="status-container" :class="statusStyle">
        <span>{{ props.data.ReviewText }}</span>
      </div>
    </el-row>
    <el-row class="mb-20px">
      <el-col :span="6">
        <el-text>姓名：{{ props.data.Name }}</el-text>
      </el-col>
      <el-col :span="6">
        <el-text>性别：{{ props.data.Sex }}</el-text>
      </el-col>
      <el-col :span="6">
        <el-text>出生日期：{{ birthday }}</el-text>
      </el-col>
      <el-col :span="6">
        <el-text>手机号：{{ props.data.PhoneNumber }}</el-text>
      </el-col>
    </el-row>
    <el-row class="mb-20px">
      <el-col :span="6">
        <el-text>人员类型：{{ props.data.WorkerTypeName }}</el-text>
      </el-col>
      <el-col :span="6">
        <el-text>职称：{{ props.data.WorkerTitleName }}</el-text>
      </el-col>
      <el-col :span="6">
        <el-text>执业级别：{{ props.data.PracticeLevel }}</el-text>
      </el-col>
      <el-col :span="6">
        <el-text>执业类别：{{ props.data.PracticeType }}</el-text>
      </el-col>
    </el-row>
    <el-row class="mb-20px">
      <el-col :span="6">
        <el-text>执业范围：{{ props.data.PracticeRange }}</el-text>
      </el-col>
      <el-col :span="6">
        <el-text>主要执业机构：{{ props.data.PracticeOrganizationName }}</el-text>
      </el-col>
      <el-col :span="6">
        <el-text>执业机构代码：{{ props.data.PracticeOrganizationCode }}</el-text>
      </el-col>
      <el-col :span="6">
        <el-text>现任职机构：{{ props.data.Organization?.Name }}</el-text>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="6">
        <el-text>所属科室：{{ props.data.Department?.Name }}</el-text>
      </el-col>
      <el-col :span="6">
        <el-text>
          临床工作年限：{{ props.data.UserWork && props.data.UserWork.ClinicalWorkYears + "年" }}
        </el-text>
      </el-col>
      <el-col :span="6">
        <el-text>推荐人：{{ props.data.Referrer || "暂无" }}</el-text>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { CertificationTabsData } from "../types/types";

defineOptions({
  name: "BasicInfo",
});

const props = defineProps<{
  data: CertificationTabsData;
}>();

const statusStyle = computed(() => {
  switch (props.data.ReviewStatus) {
    case 2:
      // 通过
      return "pass";
    case 3:
      // 拒绝
      return "refuse";
    case 0:
      // 审核中
      return "approved";
    default:
      return "";
  }
});

const birthday = computed(() => {
  return props.data.Birthday ? dayjs(props.data.Birthday).format("YYYY-MM-DD") : "";
});
const headImg = computed(() => {
  if (props.data.HeadImg) {
    return props.data.HeadImg;
  } else if (props.data.Sex?.includes("女")) {
    return "https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-doctor/default-female.png";
  } else if (props.data.Sex?.includes("男")) {
    return "https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-doctor/default-male.png";
  }

  return "";
});
</script>

<style lang="scss" scoped>
.status-container {
  width: 120px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 16px;
  font-weight: 600;

  &.pass {
    color: #10b25c;
    background: url("./imgs/agreen.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  &.approved {
    color: #0091ff;
    background: url("./imgs/approve.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  &.refuse {
    color: #e70617;
    background: url("./imgs/refuse.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}
</style>
