<template>
  <div v-loading="formLoading" class="px-20px overflow-y-auto">
    <el-form
      :ref="kFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :disabled="props.disabled"
    >
      <el-form-item label-width="0" prop="Reason">
        <el-input
          v-model="formData.Reason"
          type="textarea"
          maxlength="400"
          show-word-limit
          :autosize="{ minRows: 5, maxRows: 10 }"
          placeholder="请输入拒绝理由"
        />
      </el-form-item>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">提交</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormRules, FormInstance } from "element-plus";

interface RejectFormData {
  Reason?: string;
}

const kEnableDebug = false;
const kFormRef = "ruleFormRef";

defineOptions({
  name: "RejectForm",
});

const props = withDefaults(
  defineProps<{
    reason?: string;
    disabled?: boolean;
  }>(),
  {
    disabled: false,
  }
);

const emit = defineEmits<{
  cancel: [];
  submit: [string];
}>();

const formLoading = ref(false);
// 表单实例
const formRef = useTemplateRef<FormInstance>(kFormRef);
// 表单数据
const formData = ref<RejectFormData>({
  Reason: props.reason,
});

// 表单验证规则
const rules = reactive<FormRules<string>>({
  Reason: [{ required: true, message: "请输入理由", trigger: "blur" }],
});

// 提交表单
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      emit("submit", formData.value.Reason!);
    } else {
      kEnableDebug && console.debug("提交失败", fields);
    }
  });
}
</script>

<style lang="scss" scoped></style>
