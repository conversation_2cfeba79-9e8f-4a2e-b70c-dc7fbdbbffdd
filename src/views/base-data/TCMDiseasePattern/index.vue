<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="是否启用">
                <KSelect
                  v-model="queryParams.IsEnabled"
                  :data="[
                    { label: '是', value: true },
                    { label: '否', value: false },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="关键字">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="名称/编码"
                  clearable
                  prefix-icon="Search"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="onAddItem">新增</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="Id"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
        >
          <el-table-column prop="Key" label="名称" min-width="150" />
          <el-table-column prop="Value" label="编码" min-width="150" />
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            width="180"
            :formatter="tableDateFormat"
          />
          <el-table-column prop="IsEnabled" label="是否启用" width="80">
            <template #default="scope">
              {{ scope.row.IsEnabled === false ? "否" : "是" }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="200">
            <template #default="scope">
              <el-button link type="primary" @click="onPreviewOrEdit(scope.row, true)">
                查看
              </el-button>
              <el-button link type="primary" @click="onPreviewOrEdit(scope.row, false)">
                编辑
              </el-button>
              <el-button
                v-if="!scope.row.IsPublish"
                link
                type="primary"
                @click="onPublish(scope.row)"
              >
                发布
              </el-button>
              <el-button
                v-if="!scope.row.IsPublish"
                link
                type="primary"
                @click="onDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 添加/编辑/查看 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    :title="showDataDialog.title"
    width="60%"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <TCMDiseasePatternForm
      :data="showDataDialog.data"
      :disabled="showDataDialog.disabled"
      @cancel="showDataDialog.isShow = false"
      @submit="onConfirmSubmitItem"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { useDictData } from "@/hooks/useDictData";
import { useTableConfig } from "@/hooks/useTableConfig";
import TCMDiseasePatternForm from "./components/TCMDiseasePatternForm.vue";

interface QueryParams {
  IsEnabled?: boolean;
  Keyword?: string;
  PageIndex: number;
  PageSize: number;
}

/** 调试开关 */
const kEnableDebug = false;
defineOptions({
  name: "TCMDiseasePattern",
});

const { pageData, tableLoading, tableFluidHeight, total, tableResize, tableDateFormat } =
  useTableConfig<ReadDict>();
const { dictId, requestDictDataList, handlePublish, handleDelete } = useDictData();

/** 查询条件 */
const queryParams = reactive<QueryParams>({
  PageIndex: 1,
  PageSize: 20,
});

/** 查看/添加/编辑弹窗 */
const showDataDialog = reactive({
  isShow: false,
  title: "",
  disabled: false,
  data: {} as ReadDict, // 查看/添加/编辑详情
});

/** 点击搜索 */
function handleQuery() {
  queryParams.PageIndex = 1;
  requestTableList();
}

/** 点击添加 */
function onAddItem() {
  kEnableDebug && console.debug("点击添加");

  showDataDialog.title = "新增xx";
  showDataDialog.disabled = false;
  showDataDialog.data = {
    DictId: Number(dictId.value ?? -1),
    IsPublish: false,
  };
  showDataDialog.isShow = true;
}

/** 点击查看/编辑 */
async function onPreviewOrEdit(row?: ReadDict, disabled: boolean = false) {
  kEnableDebug && console.debug("查看/编辑", row, disabled);

  showDataDialog.title = disabled ? "查看" : "编辑";
  showDataDialog.disabled = disabled;
  showDataDialog.data = row ?? {};
  showDataDialog.isShow = true;
}

/** 确定新增提交 */
function onConfirmSubmitItem() {
  kEnableDebug && console.debug("确定提交");

  // 提交成功
  showDataDialog.isShow = false;
  ElNotification.success("提交成功");

  // 刷新列表
  requestTableList();
}

/**
 * 点击发布
 */
async function onPublish(row: ReadDict) {
  kEnableDebug && console.debug("发布", row);

  if (!row.Id) {
    ElMessage.error("Id不能为空");
    return;
  }

  const r = await handlePublish(row.Id);
  if (r) {
    queryParams.PageIndex = 1;
    requestTableList();
  }
}

/**
 * 点击删除
 */
async function onDelete(row: ReadDict) {
  kEnableDebug && console.debug("删除", row);

  if (!row.Id) {
    ElMessage.error("Id不能为空");
    return;
  }

  const r = await handleDelete(row.Id);
  if (r) {
    queryParams.PageIndex = 1;
    requestTableList();
  }
}

/**
 * 请求列表数据
 */
async function requestTableList() {
  tableLoading.value = true;
  const rules: Rule[] = [];
  if (queryParams.IsEnabled !== undefined) {
    rules.push({
      Field: "IsEnabled",
      Value: queryParams.IsEnabled,
      Operate: 3,
    });
  }
  let groupsRules: Rule[] = [];
  if (queryParams.Keyword) {
    groupsRules = [
      {
        Field: "Key",
        Value: queryParams.Keyword,
        Operate: 11,
      },
      {
        Field: "Value",
        Value: queryParams.Keyword,
        Operate: 11,
      },
    ];
  }
  const filterGroup: FilterGroup = {
    Rules: rules,
    Groups: [
      {
        Rules: groupsRules,
        Operate: 2,
      },
    ],
    Operate: 1,
  };
  const params: DictQueryParams = {
    PageCondition: {
      PageIndex: queryParams.PageIndex,
      PageSize: queryParams.PageSize,
      SortConditions: [
        {
          SortField: "Key",
          ListSortDirection: 1,
        },
      ],
    },
    FilterGroup: filterGroup,
  };
  const r = await requestDictDataList("CMDiseasesDict", params);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  pageData.value = r.Data.Rows;
  total.value = r.Data.Total;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
