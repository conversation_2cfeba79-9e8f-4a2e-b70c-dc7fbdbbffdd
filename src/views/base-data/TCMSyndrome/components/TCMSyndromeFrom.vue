<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto">
    <el-form
      :ref="kFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :disabled="props.disabled"
    >
      <el-form-item label="名称" prop="Key">
        <el-input
          v-model="formData.Key"
          placeholder="请输入名称"
          :disabled="formData.IsPublish"
          clearable
        />
      </el-form-item>
      <el-form-item label="编码" prop="Value">
        <el-input
          v-model="formData.Value"
          placeholder="请输入编码"
          :disabled="formData.IsPublish"
          clearable
        />
      </el-form-item>
      <el-form-item label="备注" prop="Remark">
        <el-input
          v-model="formData.Remark"
          placeholder="请输入备注"
          type="textarea"
          :autosize="{ minRows: 5, maxRows: 10 }"
          :disabled="formData.IsPublish"
          clearable
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="IsEnabled">
        <el-switch v-model="formData.IsEnabled" />
      </el-form-item>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormRules, FormInstance } from "element-plus";
import Dictionary_Api from "@/api/dictionary";
import { DictCreateUpdateInputDTO } from "@/api/dictionary/types";
import { useDictData } from "@/hooks/useDictData";

const { requestDictDataDetail } = useDictData();

const kEnableDebug = false;
const kFormRef = "ruleFormRef";

defineOptions({
  name: "TCMSyndromeFrom",
});

const props = defineProps<{
  dictId: string;
  id?: string;
  disabled: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

const formLoading = ref(false);
/** 表单实例 */
const formRef = useTemplateRef<FormInstance>(kFormRef);
/** 表单数据 */
const formData = reactive<DictCreateUpdateInputDTO>({
  Key: "",
  PinyinCode: "",
  IsEnabled: true,
  DictId: Number(props.dictId),
});

/** 表单验证规则 */
const rules = reactive<FormRules<DictCreateUpdateInputDTO>>({
  Key: [{ required: true, message: "请输入名称", trigger: "blur" }],
  Value: [{ required: true, message: "请输入编码", trigger: "blur" }],
});

/** 提交表单 */
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      requestAddOrUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields);
    }
  });
}

/** 添加/更新数据 */
async function requestAddOrUpdateData() {
  kEnableDebug && console.debug("添加/更新数据", formData);

  formLoading.value = true;
  const params: DictCreateUpdateInputDTO = {
    ...formData,
  };
  let r: ServerResult;
  if (props.id) {
    r = await Dictionary_Api.updateDict([params]);
  } else {
    r = await Dictionary_Api.createDict([params]);
  }
  formLoading.value = false;

  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}

/**
 * 获取基础数据详情
 */
async function requestDataDetail() {
  const r = await requestDictDataDetail({
    dictId: props.dictId,
    id: props.id!,
  });
  if (r.Type === 200) {
    Object.assign(formData, r.Data.Rows[0]);
  }

  return r;
}

onMounted(async () => {
  if (props.id) {
    // 编辑/查看
    formLoading.value = true;
    const r = await requestDataDetail();
    formLoading.value = false;
    if (r.Type !== 200) {
      ElMessage.error(r.Content);
    }
  }
});
</script>

<style lang="scss" scoped></style>
