<template>
  <el-container class="w-full h-full">
    <el-aside width="230px" class="p-10px">
      <el-tree
        class="w-full h-full p-10px"
        :data="leftTreeData"
        :loading="treeLoading"
        :props="defaultProps"
        default-expand-all
        highlight-current
        @node-click="handleNodeClick"
        @node-contextmenu="handleNodeRightClick"
      />
      <div v-show="leftMenuShow.isShow">
        <ul
          id="menu"
          class="menu"
          :style="'top:' + menuPosition.clientY + 'px;left:' + menuPosition.clientX + 'px;'"
        >
          <li
            v-if="leftMenuShow.showAdd"
            class="menu_item"
            @click="showDialog.consumableType = true"
          >
            添加
          </li>
          <li
            v-if="leftMenuShow.showEdit"
            class="menu_item"
            @click="showDialog.consumableType = true"
          >
            编辑
          </li>
        </ul>
      </div>
    </el-aside>
    <el-main class="p-10px!">
      <BaseTableSearchContainer @size-changed="tableResize">
        <template #search>
          <TBSearchContainer :is-show-toggle="true">
            <template #left>
              <el-form
                ref="queryFormRef"
                label-position="right"
                :model="queryParams"
                :inline="true"
              >
                <el-form-item prop="isEnable" label="是否启用">
                  <el-select
                    v-model="queryParams.isEnable"
                    clearable
                    placeholder="请选择"
                    :empty-values="[null, undefined]"
                    value-on-clear=""
                  >
                    <el-option label="全部" value="" />
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                  </el-select>
                </el-form-item>
                <el-form-item prop="keywords" label="关键字">
                  <el-input
                    v-model="queryParams.keywords"
                    placeholder="名称/编码/拼音码"
                    clearable
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
              </el-form>
            </template>
            <template #right>
              <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
              <el-button type="primary" @click="handleAddConsumable">添加</el-button>
            </template>
          </TBSearchContainer>
        </template>
        <template #table>
          <el-table
            ref="tableRef"
            v-loading="tableLoading"
            :data="pageData"
            highlight-current-row
            border
            :height="tableFluidHeight"
            style="text-align: center; flex: 1"
          >
            <el-table-column
              prop="Name"
              label="名称"
              :show-overflow-tooltip="true"
              align="center"
              width="180"
            />
            <el-table-column
              prop="PYM"
              label="拼音码"
              :show-overflow-tooltip="true"
              align="center"
              width="180"
            />
            <el-table-column
              prop="Code"
              label="编码"
              :show-overflow-tooltip="true"
              align="center"
              width="100"
            />
            <el-table-column
              prop="Spec"
              label="规格"
              :show-overflow-tooltip="true"
              align="center"
              width="200"
            />
            <el-table-column
              prop="PackUnit"
              label="包装单位"
              :show-overflow-tooltip="true"
              align="center"
              width="200"
            />
            <el-table-column
              prop="PackRatio"
              label="包装比例"
              :show-overflow-tooltip="true"
              align="center"
              width="200"
            />
            <el-table-column prop="IsEnable" label="是否启用" align="center" width="100">
              <template #default="scope">
                {{ scope.row.IsEnable ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column
              prop="CreatedTime"
              label="创建时间"
              :show-overflow-tooltip="true"
              align="center"
              width="200"
            />
            <el-table-column fixed="right" label="操作" width="180" align="center">
              <template #default="scope">
                <el-button
                  link
                  size="small"
                  type="primary"
                  @click="handleEditConsumable(scope.row, true)"
                >
                  查看
                </el-button>
                <el-button
                  link
                  size="small"
                  type="primary"
                  @click="handleEditConsumable(scope.row, false)"
                >
                  编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <template #pagination>
          <Pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.page"
            v-model:limit="queryParams.pageSize"
            @pagination="handleGetTableList"
          />
        </template>
      </BaseTableSearchContainer>
    </el-main>
  </el-container>
  <el-dialog
    v-model="showDialog.consumableType"
    :title="consumableTypeTitle"
    width="400"
    destroy-on-close
  >
    <ConsumableTypeContent ref="consumableTypeContentRef" :type-data="rightClickType" />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showDialog.consumableType = false">取消</el-button>
        <el-button
          type="primary"
          :loading="dialogConfirmLoading"
          @click="handleConsumableTypeSubmit"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="showDialog.consumable" :title="consumableTitle" width="800" destroy-on-close>
    <ConsumableContent
      ref="consumableContentRef"
      :classification="handleGetClassification()"
      :consumables-data="consumablesData"
      :is-disabled="isDisabledConsumable"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showDialog.consumable = false">取消</el-button>
        <el-button
          v-if="!isDisabledConsumable"
          type="primary"
          :loading="dialogConfirmLoading"
          @click="handleConsumableSubmit"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import ConsumableTypeContent from "./components/ConsumableTypeContent.vue";
import ConsumableContent from "./components/ConsumableContent.vue";
import Content_Api from "@/api/content";
import { ConsumableInputDTO, ConsumableTypeInputDTO } from "@/api/content/types";
import dayjs from "dayjs";
import { useUserStore } from "@/store";
const userStore = useUserStore();
import { useTableConfig } from "@/hooks/useTableConfig";

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<BaseConsumables>();
defineOptions({
  name: "ConsumablesManagement",
  inheritAttrs: false,
});
interface PageConsumablesType extends ConsumablesType {
  Children?: PageConsumablesType[];
}
interface PageDialogShow {
  consumableType: boolean;
  consumable: boolean;
}
const defaultProps = {
  children: "Children",
  label: "Name",
};
const treeLoading = ref<boolean>(false);
const dialogConfirmLoading = ref<boolean>(false);
const isDisabledConsumable = ref<boolean>(false);
const consumablesData = ref<ConsumableInputDTO>({
  Code: "",
  IsEnable: true,
  Name: "",
  PYM: "",
  PackRatio: 1,
  PackUnit: "",
  Type: "",
});
const queryParams = ref({
  isEnable: "",
  keywords: "",
  page: 1,
  pageSize: 20,
  type: "",
});
const leftMenuShow = ref<{
  isShow: boolean;
  showAdd: boolean;
  showEdit: boolean;
  showDelete: boolean;
}>({
  isShow: false,
  showAdd: false,
  showEdit: false,
  showDelete: false,
});
const menuPosition = ref({
  clientX: 0,
  clientY: 0,
});
const showDialog = ref<PageDialogShow>({
  consumableType: false,
  consumable: false,
});
let rightClickType: ConsumableTypeInputDTO = reactive({
  Id: "",
  Name: "",
  PYM: "",
  Code: "",
  IsEnable: true,
});
const consumableTypeContentRef = ref<InstanceType<typeof ConsumableTypeContent> | null>(null);
const consumableContentRef = ref<InstanceType<typeof ConsumableContent> | null>(null);

let leftTreeData: PageConsumablesType[] = [];
let consumableTypeTitle: string = "";
let consumableTitle: string = "添加耗材";
const handleNodeClick = (data: PageConsumablesType) => {
  if (data.Id === queryParams.value.type) {
    return;
  }
  queryParams.value.type = data.Id;
  handleQuery();
};
const handleGetConsumableCategoryList = async () => {
  try {
    treeLoading.value = true;
    const res = await Content_Api.getConsumablesTypePageData();
    if (res.Type === 200) {
      const treeData: PageConsumablesType[] = [
        {
          Name: "全部",
          Id: "",
          Children: [],
          PYM: "",
          Code: "",
          IsEnable: true,
        },
      ];
      res.Data.forEach((item) => {
        treeData[0].Children?.push({ ...item });
      });
      leftTreeData = treeData;
    }
  } catch (error) {
    ElMessage.error("获取左侧分类失败");
  } finally {
    treeLoading.value = false;
  }
};
const handleQuery = () => {
  queryParams.value.page = 1;
  handleGetTableList();
};
const handleGetTableList = async () => {
  try {
    const copyData = JSON.parse(JSON.stringify(queryParams.value));
    Object.keys(copyData).forEach((key) => {
      if (copyData[key] === "") {
        copyData[key] = undefined;
      }
    });
    tableLoading.value = true;
    const res = await Content_Api.getConsumablesPageData({
      page: copyData.page,
      pageSize: copyData.pageSize,
      keywords: copyData.keywords,
      isEnable: copyData.isEnable,
      type: copyData.type,
    });
    if (res.Type === 200) {
      res.Data.Data.forEach((item) => {
        item.CreatedTime = dayjs(item.CreatedTime).format("YYYY-MM-DD HH:mm:ss");
      });
      pageData.value = res.Data.Data;
      total.value = res.Data.TotalCount;
    }
  } catch (error) {
    console.log(error);
    ElMessage.error("获取右侧表格数据失败");
  } finally {
    tableLoading.value = false;
  }
};
const handleNodeRightClick = (event: MouseEvent, data: PageConsumablesType) => {
  const copyData: PageConsumablesType = JSON.parse(JSON.stringify(data));
  menuPosition.value.clientX = event.clientX + 10;
  var clientHeight = document.body.clientHeight;
  if (clientHeight - event.clientY <= 110) {
    menuPosition.value.clientY = clientHeight - 110;
  } else {
    menuPosition.value.clientY = event.clientY;
  }
  if (copyData.Id) {
    leftMenuShow.value.isShow = true;
    leftMenuShow.value.showEdit = true;
    leftMenuShow.value.showAdd = false;
    consumableTypeTitle = "编辑耗材类别";
  } else {
    leftMenuShow.value.isShow = true;
    leftMenuShow.value.showAdd = true;
    leftMenuShow.value.showEdit = false;
    consumableTypeTitle = "添加耗材类别";
    copyData.Name = "";
  }
  delete copyData.Children;
  rightClickType = {
    Name: copyData.Name,
    PYM: copyData.PYM,
    Code: copyData.Code,
    IsEnable: copyData.IsEnable,
    ObjectId: copyData.Id,
  };
  document.addEventListener("click", handleHideMenu);
};
const handleHideMenu = () => {
  leftMenuShow.value.isShow = false;
  document.removeEventListener("click", handleHideMenu);
};
const handleConsumableTypeSubmit = async () => {
  const params = await consumableTypeContentRef.value?.submitForm();
  if (params) {
    try {
      params.CreateTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
      params.CreatorId = userStore.userInfo.Id;
      if (!params.ObjectId) delete params.ObjectId;
      dialogConfirmLoading.value = true;
      const res = await Content_Api.insertOrUpdateConsumableType(params);
      if (res.Type === 200) {
        ElMessage.success("操作成功");
        showDialog.value.consumableType = false;
        handleGetConsumableCategoryList();
      }
    } catch (error) {
      ElMessage.error("操作失败");
    } finally {
      dialogConfirmLoading.value = false;
    }
  }
};
const handleConsumableSubmit = async () => {
  const params: ConsumableInputDTO | null = await consumableContentRef.value!.handleSubmit();
  if (params) {
    try {
      dialogConfirmLoading.value = true;
      const res = await Content_Api.insertOrUpdateConsumables(params);
      if (res.Type === 200) {
        ElMessage.success("操作成功");
        showDialog.value.consumable = false;
        handleGetTableList();
      }
    } catch (error) {
      ElMessage.error("操作失败");
    } finally {
      dialogConfirmLoading.value = false;
    }
  }
};
const handleAddConsumable = () => {
  if (!queryParams.value.type) {
    ElMessage.warning("请先选择左侧的耗材类别");
    return;
  }
  consumablesData.value = {
    Code: "",
    IsEnable: true,
    Name: "",
    PYM: "",
    PackRatio: 1,
    PackUnit: "",
    Type: queryParams.value.type,
    Spec: "",
    Urls: [],
  };
  isDisabledConsumable.value = false;
  showDialog.value.consumable = true;
};
const handleGetClassification = (): ConsumablesType[] => {
  const parentId = queryParams.value.type || consumablesData.value.Type;
  if (!parentId) {
    return [];
  }
  return leftTreeData[0].Children!.filter(
    (item) => item.Id === queryParams.value.type || item.IsEnable
  );
};
const handleEditConsumable = (data: BaseConsumables, isDisabled: boolean = false) => {
  isDisabledConsumable.value = isDisabled;
  consumableTitle = "编辑耗材";
  // queryParams.value.type = data.Type;
  consumablesData.value = {
    Code: data.Code,
    IsEnable: data.IsEnable,
    Name: data.Name,
    PYM: data.PYM,
    PackRatio: data.PackRatio,
    PackUnit: data.PackUnit,
    Spec: data.Spec,
    Type: data.Type,
    Urls: data.Urls,
    ObjectId: data.Id,
  };
  showDialog.value.consumable = true;
};

onMounted(() => {
  handleGetConsumableCategoryList();
});
onActivated(() => {
  // 调用时机为首次挂载
  // 以及每次从缓存中被重新插入时
  handleGetTableList();
});
</script>
<style scoped lang="scss">
.menu {
  width: 80px;
  position: fixed;
  border-radius: 10px;
  border: 1px solid #eeeeee;
  background-color: #ffffff;
  color: #42b983;
  padding: 0;
  .menu_item {
    line-height: 30px;
    text-align: center;
  }
  li:hover {
    background-color: #0bc65d;
    color: white;
  }
  li {
    font-size: 14px;
    list-style: none;
  }
}
</style>
