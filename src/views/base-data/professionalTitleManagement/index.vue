<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="是否启用">
                <el-select
                  v-model="queryParams.IsEnabled"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  style="width: 80px"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="Key">
                <el-input
                  v-model="queryParams.Key"
                  placeholder="名称/编码/拼音码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" icon="search" @click="handlePreviewOrEdit(null, false)">
              新增
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="Key" label="名称" align="center" />
          <el-table-column prop="Value" label="编码" align="center" />
          <el-table-column prop="PinyinCode" label="拼音码" align="center" />
          <el-table-column prop="CustomSort" label="分类" align="center">
            <template #default="scope">
              {{ handleGetWorkerType(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            width="150"
            :formatter="tableDateFormat"
            align="center"
          />
          <el-table-column prop="enable" label="是否启用" width="80" align="center">
            <template #default="scope">
              <span>{{ scope.row.IsEnabled === false ? "否" : "是" }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, true)">
                查看
              </el-button>
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, false)">
                编辑
              </el-button>
              <el-button
                v-if="!scope.row.IsPublish"
                link
                type="primary"
                @click="handlePublish(scope.row)"
              >
                发布
              </el-button>
              <el-button
                v-if="!scope.row.IsPublish"
                link
                type="danger"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog v-model="showDialog" :title="dialogTitle" width="400" destroy-on-close>
      <ProfessionalTitleContent ref="professionalTitleContentRef" :item="currentItem" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handleSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import Dictionary_Api from "@/api/dictionary";
import { getDictListData } from "@/utils/dict";
import ProfessionalTitleContent from "./components/ProfessionalTitleContent.vue";

defineOptions({
  name: "ProfessionalTitleManagement",
});

const queryParams = ref<any>({
  PageIndex: 1,
  PageSize: 20,
  IsEnabled: null,
  Key: "",
});

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const showDialog = ref<boolean>(false);
const dialogTitle = ref<string>("");
const dialogConfirmLoading = ref<boolean>(false);
const currentItem = ref<ReadDict | null>(null);
const professionalTitleContentRef = useTemplateRef("professionalTitleContentRef");
const workerTypeList = ref<ReadDict[]>([]);
provide("workerTypeList", workerTypeList);

const { tableLoading, pageData, total, tableRef, tableDateFormat, tableFluidHeight, tableResize } =
  useTableConfig<unknown>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handlePreviewOrEdit = async (row: ReadDict | null, isPreviewState: boolean) => {
  isPreview.value = row ? isPreviewState : false;
  dialogTitle.value = row ? (isPreviewState ? "查看" : "编辑") : "新增";
  currentItem.value = row;
  showDialog.value = true;
};
const handleGetTableList = async () => {
  const params = handleGetParams();
  tableLoading.value = true;
  const res = await Dictionary_Api.readDict(params);
  if (res.Type === 200) {
    pageData.value = res.Data.Rows;
    total.value = res.Data.Total;
  } else {
    ElMessage.error(res.Content);
  }
  tableLoading.value = false;
};

const handleSubmit = async () => {
  const params = await professionalTitleContentRef.value?.handleSubmit();
  if (!params) return;
  let fun = params.Id ? Dictionary_Api.updateDict : Dictionary_Api.createDict;
  dialogConfirmLoading.value = true;
  fun([params])
    .then((res) => {
      if (res.Type === 200) {
        ElNotification({
          title: "成功",
          message: params.Id ? "编辑成功" : "添加成功",
          type: "success",
        });
        showDialog.value = false;
        handleGetTableList();
      }
    })
    .catch((err) => {
      ElMessage.error(err.message);
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

const handlePublish = async (row: ReadDict) => {
  ElMessageBox.confirm("确定发布吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await Dictionary_Api.publishDict({ id: row.Id! });
    if (res.Type === 200) {
      ElNotification({
        title: "成功",
        message: "发布成功",
        type: "success",
      });
      handleGetTableList();
    }
  });
};
const handleDelete = async (row: ReadDict) => {
  ElMessageBox.confirm("确定删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await Dictionary_Api.deleteDict({ id: row.Id! });
    if (res.Type === 200) {
      ElNotification({
        title: "成功",
        message: "删除成功",
        type: "success",
      });
      handleGetTableList();
    }
  });
};

const handleGetParams = () => {
  const params = {
    PageCondition: {
      PageIndex: queryParams.value.PageIndex,
      PageSize: queryParams.value.PageSize,
      SortConditions: [{ SortField: "Key", ListSortDirection: 1 }],
    },
    FilterGroup: {
      Rules: [
        { Field: "DictId", Value: "3", Operate: 3 },
        { Field: "IsEnabled", Value: queryParams.value.IsEnabled, Operate: 3 },
      ],
      Groups: [
        {
          Rules: [
            { Field: "Key", Value: queryParams.value.Key, Operate: 11 },
            { Field: "Value", Value: queryParams.value.Key, Operate: 11 },
            { Field: "PinyinCode", Value: queryParams.value.Key, Operate: 11 },
          ],
          Operate: 2,
        },
      ],
      Operate: 1,
    },
  };
  return params;
};

const onGetWorkerType = async () => {
  const list = await getDictListData("WorkerTypeDict", {
    PageSize: 1000,
    Key: "",
    IsEnabled: true,
    IsPublish: true,
  });
  workerTypeList.value = list;
};

const handleGetWorkerType = (row: ReadDict) => {
  const workerType = workerTypeList.value.find((item) => item.Id === row.ParentId);
  return workerType?.Key;
};

onMounted(() => {
  // 获取职业类型
  onGetWorkerType();
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
