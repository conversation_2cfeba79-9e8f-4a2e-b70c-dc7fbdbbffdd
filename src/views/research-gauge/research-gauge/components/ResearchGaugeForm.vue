<template>
  <div v-loading="loading" class="p-20px overflow-y-auto h-600px">
    <el-form
      ref="ruleFormRef"
      :model="formData"
      :rules="rules"
      label-width="105px"
      :disabled="props.isDisabled"
    >
      <!-- 标签页 -->
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="基本信息" :name="0">
          <!-- 基本信息 -->
          <el-form-item label="量表名称" prop="Name">
            <el-input v-model="formData.Name" placeholder="请输入量表名称" />
          </el-form-item>
          <el-form-item label="Sign" prop="Sign">
            <el-input
              v-model.number="formData.Sign"
              class="mr-10px flex-1"
              type="number"
              placeholder="请输入Sign"
            />
            <el-popover placement="bottom" :width="200" content="唯一标识的数字，不能重复">
              <template #reference>
                <el-icon><QuestionFilled /></el-icon>
              </template>
            </el-popover>
          </el-form-item>
          <el-form-item label="StandardRule" prop="StandardRule">
            <el-input
              v-model.number="formData.StandardRule"
              class="mr-10px flex-1"
              type="number"
              placeholder="请输入StandardRule"
            />
            <el-popover
              placement="bottom"
              :width="200"
              content="1:Sum 按总分结算量表；2:Avg 按平均分结算量表；3:Group 按分组结算量表；4:Other 自定义"
            >
              <template #reference>
                <el-icon><QuestionFilled /></el-icon>
              </template>
            </el-popover>
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane label="量表内容" :name="1">
          <!-- 量表内容 -->
          <el-card
            v-for="(question, index) in formData.Questions"
            :key="index"
            class="mb-10px"
            shadow="never"
          >
            <el-row align="middle">
              <el-col :span="20">
                <div class="mb-10px">
                  <el-text tag="b">{{ question.Key }}.{{ question.Text }}</el-text>
                  <span v-if="question.Required" style="color: red">*</span>
                </div>
                <div
                  v-for="(option, optionIndex) in question.Options"
                  v-if="(question.Type === 0 || question.Type === 1) && !question.List?.length"
                  :key="`${index}-${optionIndex}`"
                  class="mb-5px"
                >
                  <el-text>{{ option.Text }}</el-text>
                </div>
                <span
                  v-if="(question.Type === 0 || question.Type === 1) && question.List?.length === 3"
                >
                  <div
                    v-for="value in generateListOptions(
                      question.List[0],
                      question.List[1],
                      question.List[2]
                    )"
                    :key="value"
                    class="mb-5px"
                  >
                    <el-text>{{ value + (question.Unit || "") }}</el-text>
                  </div>
                </span>
              </el-col>
              <el-col v-if="!props.isDisabled" :span="4">
                <el-row>
                  <span
                    class="flex-1 p-10px flex items-center justify-center"
                    @click="onEditQuestion(index)"
                  >
                    <el-icon size="20px"><Edit /></el-icon>
                  </span>
                  <span
                    class="flex-1 p-10px flex items-center justify-center"
                    @click="onDeleteQuestion(index)"
                  >
                    <el-icon size="20px" color="#f56c6c"><Delete /></el-icon>
                  </span>
                </el-row>
              </el-col>
            </el-row>
          </el-card>
          <el-row v-if="!props.isDisabled" justify="center">
            <el-button type="primary" @click="onAddQuestion(0)">添加一道选择题</el-button>
            <el-button type="primary" @click="onAddQuestion(2)">添加一道文本题</el-button>
            <el-button type="primary" @click="onAddQuestion(3)">添加一道数值题</el-button>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="$emit('cancel')">取消</el-button>
    <el-button v-if="!props.isDisabled" type="primary" @click="onSubmitForm(ruleFormRef)">
      确定
    </el-button>
  </div>
  <!-- 添加/编辑题目 -->
  <el-dialog
    v-model="showQuestionDialog.isShow"
    :title="showQuestionDialog.title"
    width="900px"
    destroy-on-close
    @close="showQuestionDialog.isShow = false"
  >
    <ResearchGaugeQuestion
      :question="showQuestionDialog.question"
      @cancel="showQuestionDialog.isShow = false"
      @submit="onConfirmSubmitQuestion"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { FormRules, FormInstance } from "element-plus";
import { deepRemoveNullValues } from "@/utils/utils";
import TenantHxunion_Api from "@/api/tenant-hxunion";

import ResearchGaugeQuestion from "./ResearchGaugeQuestion.vue";

const kEnableDebug = false;
const props = defineProps<{
  gauge: RiskWarningGauge;
  isDisabled: boolean;
}>();
const emit = defineEmits(["cancel", "submit"]);
onMounted(() => {
  kEnableDebug && console.log("onMounted props", props);
  try {
    const data = JSON.parse(JSON.stringify(props.gauge));
    Object.assign(formData, data);
  } catch (error) {
    kEnableDebug && console.error("onMounted props", error);
  }
});

const loading = ref(false);
// 表单实例
const ruleFormRef = ref<FormInstance>();
// 表单数据
const formData = reactive<RiskWarningGauge>({});
// 表单验证规则
const rules = reactive<FormRules<RiskWarningGauge>>({
  Name: [{ required: true, message: "请输入量表名称", trigger: "blur" }],
  Sign: [
    {
      required: true,
      message: "请输入Sign",
      trigger: "blur",
    },
  ],
  StandardRule: [
    {
      required: true,
      message: "请输入StandardRule",
      trigger: "blur",
    },
  ],
});
const activeName = ref(0);

// 添加/编辑题目弹框
const showQuestionDialog = reactive({
  isShow: false,
  title: "",
  question: {} as RiskWarningGaugeQuestion,
});

/** 根据 List 生成列表选项 */
function generateListOptions(start: number, end: number, step: number): number[] {
  const options: number[] = [];
  for (let i = start; i <= end; i += step) {
    options.push(i);
  }
  return options;
}

/** 添加一道题 */
function onAddQuestion(type: number) {
  kEnableDebug && console.debug("添加题目，类型", type);

  showQuestionDialog.question = {
    Required: true,
    Type: type,
    Key: (formData.Questions?.length ?? 0) + 1,
  };
  showQuestionDialog.title = "添加题目";
  showQuestionDialog.isShow = true;
}

/** 编辑题目 */
function onEditQuestion(index: number) {
  kEnableDebug && console.debug("编辑题目", index);

  showQuestionDialog.question = formData.Questions![index];
  showQuestionDialog.title = "编辑题目";
  showQuestionDialog.isShow = true;
}

/** 点击确定提交题目 */
function onConfirmSubmitQuestion(question: RiskWarningGaugeQuestion) {
  kEnableDebug && console.debug("题目弹框 - 确定", question);

  const index = formData.Questions?.findIndex((e) => e.Key === question.Key);
  if (index !== -1) {
    formData.Questions![index!] = question;
  } else {
    formData.Questions?.push(question);
  }
  showQuestionDialog.isShow = false;
}

/** 删除题目 */
function onDeleteQuestion(index: number) {
  kEnableDebug && console.log("onDeleteQuestion", index);
  formData.Questions?.splice(index, 1);
}

/** 提交表单 */
function onSubmitForm(ruleFormRef: FormInstance | undefined) {
  if (!ruleFormRef) return;

  ruleFormRef.validate((valid, fields) => {
    if (!valid) {
      kEnableDebug && console.debug("量表 - 提交失败", fields, formData);
      return;
    }

    if (!formData.Questions?.length) {
      ElMessage.warning("请至少添加一道题目");
      activeName.value = 1;
      return;
    }

    requestAddOrUpdateGauge();
  });
}

/** 添加、更新量表 */
async function requestAddOrUpdateGauge() {
  loading.value = true;
  const gauge: RiskWarningGauge = deepRemoveNullValues({ ...formData });
  gauge.Questions = gauge.Questions?.map((e, index) => {
    e.Key = index + 1;
    return e;
  });
  if (gauge.Id) {
    gauge.ObjectId = gauge.Id;
  }
  kEnableDebug && console.debug("添加/更新量表 - 处理后", gauge);

  const r = await TenantHxunion_Api.insertOrUpdate(gauge);
  loading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 提交成功
  emit("submit");
}
</script>

<style lang="scss" scoped></style>
