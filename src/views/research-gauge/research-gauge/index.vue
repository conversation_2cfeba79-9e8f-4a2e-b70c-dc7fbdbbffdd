<template>
  <div v-loading="tableLoading" class="app-container">
    <el-col>
      <!-- 操作栏 -->
      <TBSearchContainer>
        <template #left>
          <div />
        </template>
        <template #right>
          <el-button class="mb-15px mr-10px" type="primary" @click="onAddGauge">添加</el-button>
        </template>
      </TBSearchContainer>
      <!-- 量表列表 -->
      <el-card shadow="never">
        <el-table
          ref="tableRef"
          :data="tableData"
          highlight-current-row
          border
          :height="tableHeight"
          style="text-align: left; flex: 1"
        >
          <el-table-column fixed prop="Name" label="名称" align="center" />
          <el-table-column prop="Sign" label="Sign" align="center" width="200" />
          <el-table-column prop="StandardRule" label="StandardRule" align="center" width="200" />
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            :show-overflow-tooltip="true"
            align="center"
            width="300"
          />
          <el-table-column fixed="right" label="操作" width="300" align="center">
            <template #default="scope">
              <el-button link size="small" type="primary" @click="onEditGauge(scope.row, true)">
                查看
              </el-button>
              <el-button link size="small" type="primary" @click="onEditGauge(scope.row, false)">
                编辑
              </el-button>
              <el-button link size="small" type="primary" @click="onReferenceGauge(scope.row)">
                引用
              </el-button>
              <el-button link size="small" type="primary" @click="onPushGauge(scope.row)">
                推送
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="requestGaugeList"
        />
      </el-card>
    </el-col>

    <!-- 添加/编辑/查看量表 -->
    <el-dialog
      v-model="showGaugeDialog.isShow"
      :title="showGaugeDialog.title"
      width="800"
      destroy-on-close
      @close="showGaugeDialog.isShow = false"
    >
      <ResearchGaugeForm
        :gauge="showGaugeDialog.gauge"
        :is-disabled="showGaugeDialog.isDisabled"
        @cancel="showGaugeDialog.isShow = false"
        @submit="onConfirmSubmitGauge"
      />
    </el-dialog>
    <!-- 选择机构 -->
    <el-dialog v-model="showOrgDialog" title="选择机构" width="700px" destroy-on-close>
      <HospitalTransfer
        :loading="orgDialogLoading"
        @cancel="showOrgDialog = false"
        @submit="onConfirmOrganizations"
      />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import TenantHxunion_Api from "@/api/tenant-hxunion";
import useOrgDialog from "@/hooks/useOrgDialog";
import ResearchGaugeForm from "./components/ResearchGaugeForm.vue";

// 是否开启调试
const kEnableDebug = false;

defineOptions({
  name: "ResearchGauge",
  inheritAttrs: false,
});

const { showOrgDialog, orgDialogLoading } = useOrgDialog();

const tableHeight = ref<number>(document.body.clientHeight - 166 - 130);
const tableLoading = ref(false);

// 查询参数
const queryParams = reactive({
  pageIndex: 1,
  pageSize: 20,
});
const total = ref<number>(0);

// 量表列表
const tableData = ref<RiskWarningGauge[]>([]);

// 弹框
const showGaugeDialog = reactive({
  isShow: false,
  title: "",
  isDisabled: false,
  isAdd: false,
  actionIndex: 0,
  gauge: {} as RiskWarningGauge,
});

// 引用的量表
var referenceGauge = undefined as RiskWarningGauge | undefined;

/** 点击添加 */
async function onAddGauge() {
  kEnableDebug && console.debug("添加量表");

  showGaugeDialog.gauge = {
    Questions: [],
  };
  showGaugeDialog.isAdd = true;
  showGaugeDialog.actionIndex = 0;
  showGaugeDialog.isDisabled = false;
  showGaugeDialog.title = "添加表单";
  showGaugeDialog.isShow = true;
}

/** 点击查看/编辑量表 */
async function onEditGauge(data: RiskWarningGauge, isDisabled: boolean = false) {
  kEnableDebug && console.debug("编辑量表", data, isDisabled);
  if (!data.Id) {
    ElMessage.error("量表id不能为空");
    return;
  }

  // 获取量表详情
  tableLoading.value = true;
  const r = await TenantHxunion_Api.getGaugeDetailById(data.Id);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }

  showGaugeDialog.gauge = r.Data;
  showGaugeDialog.isAdd = false;
  showGaugeDialog.actionIndex = 0;
  showGaugeDialog.isDisabled = isDisabled;
  showGaugeDialog.title = isDisabled ? "查看表单" : "编辑表单";
  showGaugeDialog.isShow = true;
}

/** 点击推送量表 */
async function onPushGauge(data: RiskWarningGauge) {
  kEnableDebug && console.debug("推送量表", data);

  const gaugeId = data.Id;
  if (!gaugeId) {
    ElMessage.error("量表id为空");
    return;
  }

  tableLoading.value = true;
  const r = await TenantHxunion_Api.pushGauge(gaugeId);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  } else {
    ElNotification.success("推送成功");
  }
}

/** 点击引用量表 */
function onReferenceGauge(data: RiskWarningGauge) {
  kEnableDebug && console.debug("引用量表", data);

  referenceGauge = data;
  showOrgDialog.value = true;
}

/** 点击选择机构 */
async function onConfirmOrganizations(orgIds: string[]) {
  kEnableDebug && console.debug("选择机构", orgIds);

  const gaugeId = referenceGauge?.Id;
  if (!gaugeId) {
    ElMessage.error("量表id为空");
    referenceGauge = undefined;
    return;
  }

  orgDialogLoading.value = true;
  const r = await TenantHxunion_Api.referenceGauge({
    GaugeId: gaugeId,
    OrgIds: orgIds,
  });
  referenceGauge = undefined;
  orgDialogLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  } else {
    showOrgDialog.value = false;
    ElNotification.success("引用成功");
  }
}

/** 点击确定提交量表 */
async function onConfirmSubmitGauge() {
  kEnableDebug && console.debug("量表提交成功");

  showGaugeDialog.isShow = false;
  showGaugeDialog.gauge = {};
  requestGaugeList();
}

/** 请求量表列表 */
async function requestGaugeList() {
  tableLoading.value = true;
  const r = await TenantHxunion_Api.getPage({
    PageIndex: queryParams.pageIndex,
    PageSize: queryParams.pageSize,
  });
  if (r.Type !== 200) {
    tableLoading.value = false;
    ElMessage.error(r.Content);
  }

  total.value = r.Data.TotalCount;
  tableData.value = r.Data.Data;
  tableLoading.value = false;
}

onMounted(async () => {
  requestGaugeList();
});
</script>

<style lang="scss" scoped></style>
