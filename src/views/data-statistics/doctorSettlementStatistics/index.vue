<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  unlink-panels
                  class="w-300px!"
                />
              </el-form-item>
              <el-form-item label="角色">
                <KSelect
                  v-model="queryParams.RoleName"
                  :data="[
                    { label: '护士', value: '护士' },
                    { label: '治疗师', value: '治疗师' },
                    { label: '医生', value: '医生' },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="入驻机构">
                <HospitalSelect
                  v-model="queryParams.OrgIds"
                  :scopeable="false"
                  @change="
                    () => {
                      queryParams.DeptId = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect
                  v-model="queryParams.DeptId"
                  :org-id="queryParams.OrgIds"
                  :scopeable="false"
                  :disabled="!queryParams.OrgIds"
                />
              </el-form-item>
              <el-form-item label="关键字">
                <el-input
                  v-model="queryParams.Keyword"
                  prefix-icon="Search"
                  placeholder="医生姓名、手机号"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="DoctorId"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
        >
          <el-table-column prop="RoleName" label="角色" width="80" />
          <el-table-column prop="Name" label="姓名" min-width="80" show-overflow-tooltip />
          <el-table-column
            prop="PhoneNumber"
            label="手机号"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column prop="OrgName" label="入驻机构" min-width="150" show-overflow-tooltip />
          <el-table-column prop="DeptName" label="入驻科室" min-width="150" show-overflow-tooltip />
          <el-table-column
            prop="SettleAmount"
            label="结算金额"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="RegisterAmount"
            label="问诊/咨询费"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="PrescriptionAmount"
            label="开方结算"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="GuideAmount"
            label="指导结算"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="DeptChiefAmount"
            label="管理结算（科主任）"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="PresidentAmount"
            label="管理结算（院长）"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column
            prop="NurseChiefAmount"
            label="管理结算（护士长）"
            min-width="80"
            show-overflow-tooltip
          />
          <el-table-column prop="RefundAmount" label="退费" min-width="80" show-overflow-tooltip />
          <el-table-column fixed="right" label="操作" width="100">
            <template #default="scope">
              <el-button link type="primary" @click="onPreviewDetail(scope.row)">
                查看明细
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <el-dialog
    v-model="showDataDialog.isShow"
    title="查看明细"
    destroy-on-close
    width="75%"
    @close="showDataDialog.isShow = false"
  >
    <DoctorSettlementDetails
      :data="showDataDialog.data"
      :start-time="queryParams.StartTimeDt"
      :end-time="queryParams.EndTimeDt"
      @cancel="showDataDialog.isShow = false"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { convertToRedashParams } from "@/utils/serviceUtils";
import Report_Api from "@/api/report";
import { DoctorSettlementStatisticsInputDTO } from "@/api/report/types";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";
import DoctorSettlementDetails from "./components/DoctorSettlementDetails.vue";

const { datePickerShortcuts } = useDateRangePicker();

interface QueryParams extends RedashParameters<DoctorSettlementStatisticsInputDTO> {}

// 调试开关
const kEnableDebug = false;
defineOptions({
  name: "DoctorSettlementStatistics",
});

const { kTableRef, pageData, tableLoading, tableFluidHeight, total, tableResize } =
  useTableConfig<DoctorSettlementRedash>();

// 查询条件
const queryParams = reactive<QueryParams>({
  StartTimeDt: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
  EndTimeDt: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  DeptId: undefined,
  Keyword: undefined,
  OrgIds: undefined,
  RoleName: undefined,
  pageIndex: 1,
  pageSize: 20,
});

// 定义 dateRange
const dateRange = computed({
  get() {
    // 从 queryParams 中获取日期范围
    return [queryParams.StartTimeDt, queryParams.EndTimeDt];
  },
  set(newValue) {
    // 当用户选择日期范围时，更新 queryParams
    if (newValue && newValue.length === 2) {
      queryParams.StartTimeDt = newValue[0].split(" ")[0] + " 00:00:00";
      queryParams.EndTimeDt = newValue[1].split(" ")[0] + " 23:59:59";
    }
  },
});

// 查看明细
const showDataDialog = reactive({
  isShow: false,
  data: {} as DoctorSettlementRedash,
});

// 点击搜索
function handleQuery() {
  queryParams.pageIndex = 1;
  requestTableList();
}

// 点击查看
function onPreviewDetail(row: DoctorSettlementRedash) {
  kEnableDebug && console.debug("查看", row);

  if (!row.DoctorId) {
    ElMessage.error("医生Id为空");
    return;
  }

  showDataDialog.data = row;
  showDataDialog.isShow = true;
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;

  const params = convertToRedashParams(queryParams, "Report_DoctorSettlementStatistics");
  const r = await Report_Api.getRedashList<DoctorSettlementRedash>(params);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  pageData.value = r.Data.Data;
  total.value = r.Data.TotalCount;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
