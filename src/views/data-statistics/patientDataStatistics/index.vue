<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="是否展示无数据医院">
                <el-select
                  v-model="queryParams.DisplayEmptyData"
                  placeholder="请选择"
                  style="width: 100px"
                >
                  <el-option label="是" value="1" />
                  <el-option label="否" value="0" />
                </el-select>
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              :disabled="pageData.length <= 0"
              :loading="exportLoading"
              @click="handleExportExcel"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          :header-cell-style="headerCellStyle"
          highlight-current-row
          style="text-align: center; flex: 1"
          show-summary
          :summary-method="getSummaries"
        >
          <el-table-column
            prop="OrganizationName"
            label="医院名称"
            :filters="filterName"
            :filter-method="filterMethod"
            :show-overflow-tooltip="true"
            width="100"
            align="center"
          />
          <el-table-column
            prop="CityName"
            label="地区"
            :filters="filterCityName"
            :filter-method="filterMethod"
            :show-overflow-tooltip="true"
            width="60"
            align="center"
          />
          <el-table-column
            prop="newUserCount"
            label="新增用户数"
            :show-overflow-tooltip="true"
            align="center"
          />
          <el-table-column
            prop="newUserAuthCount"
            label="新增已认证用户数"
            :show-overflow-tooltip="true"
            align="center"
          />
          <el-table-column
            prop="newPatientCount"
            label="新增患者数"
            :show-overflow-tooltip="true"
            align="center"
          />
          <el-table-column
            prop="userCount"
            label="累计用户数"
            :show-overflow-tooltip="true"
            align="center"
          />
          <el-table-column
            prop="userAuthCount"
            label="累计已认证用户数"
            :show-overflow-tooltip="true"
            align="center"
          />
          <el-table-column
            prop="patientCount"
            label="累计患者数"
            :show-overflow-tooltip="true"
            align="center"
          />
        </el-table>
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store";
import Report_Api from "@/api/report";
import {
  ExportTaskRedashDTO,
  PatientsSummaryInputDTO,
  PatientsSummaryItem,
} from "@/api/report/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import { convertToRedashParams, exportExcel, getExportCols } from "@/utils/serviceUtils";
const userStore = useUserStore();
import dayjs from "dayjs";
import { Filters } from "element-plus/es/components/table/src/table-column/defaults";
import { ExportEnum } from "@/enums/Other";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();

defineOptions({
  name: "PatientDataStatistics",
});

const queryParams = ref<PatientsSummaryInputDTO>({
  DisplayEmptyData: "0",
  EndTime: dayjs().format("YYYY-MM-DD 23:59:59"),
  StartTime: dayjs().format("YYYY-MM-DD 00:00:00"),
  LoginUserId: "",
  PageIndex: 1,
  PageSize: 20,
  orgIds: null,
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-DD"),
  dayjs().format("YYYY-MM-DD"),
]);
const filterName = ref<Filters>([]);
const filterCityName = ref<Filters>([]);
const exportLoading = ref<boolean>(false);
const queryResultId = ref<number>(0);

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<PatientsSummaryItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const headerCellStyle = ({ column }: { column: any }) => {
  const colorsMap = {
    "#65A33E": ["newUserCount", "newUserAuthCount", "newPatientCount"],
  };

  // 查找当前列使用的背景色
  for (const [bgColor, columns] of Object.entries(colorsMap)) {
    if (columns.includes(column.property)) {
      return {
        backgroundColor: bgColor,
        color: "#303133",
        fontWeight: "bold",
      };
    }
  }

  // 默认样式
  return {
    backgroundColor: "#f5f7fa",
    color: "#606266",
  };
};
const handleGetTableList = async () => {
  queryParams.value.LoginUserId = userStore.userInfo.Id;
  tableLoading.value = true;
  const redashParams = convertToRedashParams(queryParams.value, "Report_PatientsSummary");
  redashParams.pageSize = 99999;
  const res = await Report_Api.getRedashList<PatientsSummaryItem>(redashParams);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
    queryResultId.value = res.Data.QueryResultId;
    handleGetFilterData();
  }
  tableLoading.value = false;
};

const handleGetFilterData = () => {
  // 对筛选出来的数据进行去重
  const newDataName = pageData.value.map((v) => ({
    text: v.OrganizationName,
    value: v.OrganizationName,
  }));
  filterName.value = [...new Map(newDataName.map((item) => [item.text, item])).values()];
  const newDataCityName = pageData.value.map((v) => ({
    text: v.CityName,
    value: v.CityName,
  }));
  filterCityName.value = [...new Map(newDataCityName.map((item) => [item.text, item])).values()];
};

const filterMethod = (value: string, row: PatientsSummaryItem, column: any) => {
  const property = column["property"];
  return row[property as keyof PatientsSummaryItem] === value;
};

const handleExportExcel = async () => {
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  const exportParams = convertToRedashParams<PatientsSummaryInputDTO>(
    copyData,
    "Report_PatientsSummary"
  );
  const params: ExportTaskRedashDTO = {
    Cols: getExportCols(tableRef.value!.columns as any, "@"),
    ExecutingParams: exportParams.parameters,
    ExportWay: ExportEnum.PlainMySql,
    FileName: `患者数据统计-${Date.now()}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId.value,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.value.PageIndex,
    PageSize: queryParams.value.PageSize,
    QueryName: "Report_PatientsSummary",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
};

watch(timeRange, (newVal) => {
  queryParams.value.StartTime = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndTime = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  // 调用时机为首次挂载
  // 以及每次从缓存中被重新插入时
  handleGetTableList();
});

const getSummaries = (param: { columns: any[]; data: any[] }) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = "合计";
      return;
    }
    if (index === 1) {
      sums[index] = "0";
      return;
    }

    const values = data.map((item) => Number(item[column.property]) || 0);
    const sum = values.reduce((prev, curr) => {
      return prev + curr;
    }, 0);

    sums[index] = `${sum}`;
  });

  return sums;
};
</script>

<style lang="scss"></style>
