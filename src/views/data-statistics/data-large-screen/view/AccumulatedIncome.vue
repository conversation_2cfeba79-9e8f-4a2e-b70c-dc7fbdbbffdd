<template>
  <div ref="accumulated-income" class="accumulated-income">
    <Title title="累计收入/今日收入&详情">
      <div style="padding: 0 10px 0 20px">
        <div class="accumulated-income-sr">累计收入</div>
        <div class="accumulated-income-sr-box">
          <div v-for="(i, index) in totalAmount" :key="index" class="accumulated-income-box">
            {{ i }}
          </div>
        </div>
        <div class="accumulated-income-sr accumulated-income-jr">今日收入</div>
        <div class="accumulated-income-sr-box">
          <div v-for="(i, index) in dailyAmount" :key="index" class="accumulated-income-box">
            {{ i }}
          </div>
        </div>
      </div>
    </Title>
  </div>
</template>
<script setup lang="ts">
import Report_Api from "@/api/report";
import Title from "./Title.vue";

const totalAmount = ref("0");
const dailyAmount = ref("0");

const loadData = async () => {
  const res = await Report_Api.getRedashList<{
    TotalAmount: string;
    DailyAmount: string;
  }>({
    queryName: "Report_PlatformRevenueStatistics",
    parameters: {},
    maxAge: 0,
    JobWaitingMs: 30000,
    pageIndex: 1,
    pageSize: 20,
  });
  if (res.Type === 200) {
    totalAmount.value = onFormatData(res.Data.Data[0].TotalAmount);
    dailyAmount.value = onFormatData(res.Data.Data[0].DailyAmount);
  }
};

const onFormatData = (count: string) => {
  let countMat = count;
  if (count === "") {
    countMat = "0";
  }
  const forMatCount = Number(countMat).toFixed(2);
  const arr = [];
  if (forMatCount.length < 10) {
    for (let i = 0; i < 10 - forMatCount.length; i++) {
      arr.unshift("0");
    }
  }
  return arr.join("") + forMatCount;
};

onMounted(() => {
  loadData();
});

defineExpose({
  loadData,
});
</script>
<style scoped lang="scss">
.accumulated-income {
  &-sr {
    margin-top: 4%;
    margin-bottom: 4%;
    margin-left: 2%;
    text-align: center;
    line-height: 28px;
    width: 100px;
    height: 28px;
    background:
      linear-gradient(270deg, #002567 0%, #184687 49%, #00255d 100%),
      linear-gradient(
        270deg,
        rgba(54, 198, 255, 0.5) 0%,
        rgba(113, 208, 255, 0.16) 55%,
        rgba(145, 214, 255, 0) 100%
      );
    border-radius: 4px;
    border: 2px solid;
    border-image: linear-gradient(
        270deg,
        rgba(169, 235, 255, 0.1),
        rgba(149, 226, 255, 0.4),
        rgba(115, 212, 255, 1),
        rgba(113, 211, 255, 0.54),
        rgba(113, 211, 255, 0.1)
      )
      2 2;
    &-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      margin-left: 10px;
    }
  }
  &-jr {
    background: linear-gradient(270deg, #3d1e04 0%, #876a18 49%, #3d1e04 100%);
    border-radius: 4px;
    border: 2px solid;
    border-image: linear-gradient(
        270deg,
        rgba(255, 219, 113, 0.1),
        rgba(255, 219, 113, 0.4),
        rgba(255, 183, 115, 1),
        rgba(255, 219, 113, 0.54),
        rgba(255, 219, 113, 0.1)
      )
      2 2;
  }
  &-box {
    width: 26px;
    height: 30px;
    box-shadow: inset 0px 0px 11px 2px rgba(83, 196, 255, 0.83);
    border: 1px solid #1adbff;
    text-align: center;
    line-height: 30px;
  }
}
</style>
