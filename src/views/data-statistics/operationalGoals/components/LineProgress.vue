<template>
  <div class="progress">
    <div class="progress-bar">
      <div class="progress-bar__outer" :style="{ height: `${strokeWidth}px` }">
        <div
          v-if="middleProgress && Object.keys(middleProgress).length > 0"
          class="progress-bar__middle"
          :style="{
            width: `${getPercentage(middleProgress.percentage)}%`,
            backgroundColor:
              typeof middleProgress.color === 'function'
                ? middleProgress.color(middleProgress.percentage)
                : middleProgress.color,
          }"
        />
        <div
          class="progress-bar__inner"
          :style="{
            width: `${getPercentage(percentage)}%`,
            backgroundColor: typeof color === 'function' ? color(percentage) : color,
            textAlign: textAlign,
          }"
        >
          <div
            v-if="textInside"
            class="progress-bar__innerText"
            :style="{
              paddingLeft:
                textAlign === 'left' || textAlign === 'right' ? `${strokeWidth / 2}px` : '0px',
              paddingRight:
                textAlign === 'left' || textAlign === 'right' ? `${strokeWidth / 2}px` : '0px',
              ...textStyle,
            }"
          >
            {{ format(percentage) }}
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="!textInside"
      class="progress__text"
      :style="{
        ...textStyle,
      }"
    >
      <span>{{ format(percentage) }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "LineProgress",
});

const props = withDefaults(
  defineProps<{
    /** 进度 (0-100) */
    percentage: number;
    /** 进度条显示文字内置在进度条内 */
    textInside?: boolean;
    /** 进度条的宽度 */
    strokeWidth?: number;
    /** 进度条背景色 */
    color?: string | ((percentage: number) => string);
    /** 进度条文字对齐方式 */
    textAlign?: "left" | "center" | "right";
    /** 进度文字样式 */
    textStyle?: Record<string, string>;
    /** 指定进度条文字内容 */
    format?: (percentage: number) => string;

    /**
     * 中间层进度条，在背景条和前景条中间，用于提示
     */
    middleProgress?: {
      percentage: number;
      color: string | ((percentage: number) => string);
    };
  }>(),
  {
    textInside: false,
    strokeWidth: 6,
    color: "#25B8A3",
    fontSize: 14,
    format: (percentage: number) => `${percentage}%`,
    textAlign: "right",
  }
);

/** 确保进度在 0~100 之间 */
function getPercentage(percentage: number) {
  return Math.max(0, Math.min(100, percentage));
}
</script>

<style lang="scss" scoped>
.progress {
  position: relative;
  line-height: 1;
  display: flex;
  align-items: center;

  &-bar {
    flex-grow: 1;
    box-sizing: border-box;

    &__outer {
      height: 6px;
      background-color: #ebeef5;
      position: relative;
      vertical-align: middle;
      border-radius: 100px;
      overflow: hidden;
    }

    &__middle {
      position: absolute;
      left: 0px;
      top: 0px;
      height: 100%;
      border-radius: 100px;
      transition: width 0.6s;
      animation-duration: 3s;
    }

    &__inner {
      position: absolute;
      left: 0px;
      top: 0px;
      height: 100%;
      line-height: 1;
      border-radius: 100px;
      white-space: nowrap;
      transition: width 0.6s;
      animation-duration: 3s;
    }

    &__innerText {
      display: inline-block;
      vertical-align: middle;
      color: #ffffff;
      font-size: 12px;
    }
  }

  &__text {
    font-size: 14px;
    color: #666666;
    margin-left: 5px;
    min-width: 50px;
    line-height: 1;
  }
}
</style>
