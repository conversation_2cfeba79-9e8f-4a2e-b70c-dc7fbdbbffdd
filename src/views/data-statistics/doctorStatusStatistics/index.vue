<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="医院">
                <HospitalSelect
                  v-model="queryParams.orgIdList"
                  :scopeable="true"
                  collapse-tags
                  clearable
                  filterable
                  multiple
                  @change="
                    () => {
                      queryParams.deptIdList = undefined;
                      queryParams.assistantIdList = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect
                  v-model="queryParams.deptIdList"
                  :org-id="queryParams.orgIdList?.[0]"
                  :disabled="queryParams.orgIdList?.length !== 1"
                  collapse-tags
                  clearable
                  filterable
                  multiple
                />
              </el-form-item>
              <el-form-item label="医助">
                <UserSelect
                  v-model="queryParams.assistantIdList"
                  placeholder="请选择医助"
                  :org-ids="queryParams.orgIdList"
                  :dept-ids="queryParams.deptIdList"
                  :scopeable="false"
                  :role-types="['assistant']"
                  collapse-tags
                  clearable
                  filterable
                  multiple
                />
              </el-form-item>
              <el-form-item label="时间">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  :disabled-date="handleDisabledDate"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  unlink-panels
                  class="w-300px!"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              :disabled="pageData.length === 0"
              :loading="exportLoading"
              @click="onExport"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <SortTable
          :loading="tableLoading"
          row-key="DocUserId"
          :header-height="70"
          :estimated-row-height="50"
          :columns="columns"
          :data="pageData"
          :height="tableFluidHeight"
          :width="tableFluidWidth"
          fixed
        />
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100, 500, 1000]"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 添加/编辑备注 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    title="备注"
    width="500"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <RemarkForm
      :data="showDataDialog.data"
      @cancel="showDataDialog.isShow = false"
      @submit="onConfirmSubmitItem"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { convertToRedashParams, exportExcel, getExportTable2Columns } from "@/utils/serviceUtils";
import Report_Api from "@/api/report";
import { ExportEnum } from "@/enums/Other";
import {
  DoctorStatusRedash,
  DoctorStatusStatisticsInputDTO,
  ExportTaskRedashDTO,
} from "@/api/report/types";
import { useUserStore } from "@/store";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";
import { TableV2FixedDir } from "element-plus";
import { TableV2Column } from "@/components/SortTable/types";
import RemarkForm from "./components/RemarkForm.vue";

const { datePickerShortcuts } = useDateRangePicker();

interface QueryParams extends RedashParameters<DoctorStatusStatisticsInputDTO> {
  orgIdList?: string[];
  deptIdList?: string[];
  assistantIdList?: string[];
}

// 调试开关
const kEnableDebug = false;
defineOptions({
  name: "DoctorStatusStatistics",
});

// 查询条件
const queryParams = reactive<QueryParams>({
  startTimeDt: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
  endTimeDt: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  LoginUserId: useUserStore().userInfo.Id,
  assistantIds: undefined,
  deptIds: undefined,
  orgIds: undefined,
  pageIndex: 1,
  pageSize: 20,
});

// 定义 dateRange
const dateRange = computed({
  get() {
    // 从 queryParams 中获取日期范围
    return [queryParams.startTimeDt, queryParams.endTimeDt];
  },
  set(newValue) {
    // 当用户选择日期范围时，更新 queryParams
    if (newValue && newValue.length === 2) {
      queryParams.startTimeDt = newValue[0].split(" ")[0] + " 00:00:00";
      queryParams.endTimeDt = newValue[1].split(" ")[0] + " 23:59:59";
    }
  },
});

watch(
  () => queryParams.orgIdList,
  (newVal) => {
    if (!newVal?.length) {
      queryParams.orgIds = undefined;
    } else {
      queryParams.orgIds = newVal?.join(",");
    }
  }
);

watch(
  () => queryParams.deptIdList,
  (newVal) => {
    if (!newVal?.length) {
      queryParams.deptIds = undefined;
    } else {
      queryParams.deptIds = newVal?.join(",");
    }
  }
);

watch(
  () => queryParams.assistantIdList,
  (newVal) => {
    if (!newVal?.length) {
      queryParams.assistantIds = undefined;
    } else {
      queryParams.assistantIds = newVal?.join(",");
    }
  }
);

// 禁用日期
function handleDisabledDate(date: Date) {
  return date.getTime() > Date.now();
}

// 点击搜索
function handleQuery() {
  queryParams.pageIndex = 1;
  requestTableList();
}

const {
  pageData,
  tableLoading,
  tableFluidHeight,
  tableFluidWidth,
  total,
  tableResize,
  exportLoading,
} = useTableConfig<DoctorStatusRedash>();

/** 表格列配置 */
const columns: TableV2Column<DoctorStatusRedash>[] = [
  {
    key: "Name",
    title: "姓名",
    width: 80,
    minWidth: 80,
  },
  {
    key: "PhoneNumber",
    title: "手机号",
    width: 110,
    minWidth: 110,
  },
  {
    key: "Sex",
    title: "性别",
    width: 60,
    minWidth: 60,
  },
  {
    key: "Age",
    title: "年龄",
    width: 60,
    minWidth: 60,
  },
  {
    key: "OrgName",
    title: "医院",
    width: 180,
    minWidth: 180,
  },
  {
    key: "DeptName",
    title: "科室",
    width: 180,
    minWidth: 180,
  },
  {
    key: "AssistantName",
    title: "医助",
    width: 110,
    minWidth: 110,
  },
  {
    key: "PracticeLevel",
    title: "职称",
    width: 90,
    minWidth: 90,
  },
  {
    key: "RoleName",
    title: "角色",
    width: 70,
    minWidth: 70,
  },
  {
    key: "newPatientCount",
    title: "新增患者数",
    width: 80,
    minWidth: 80,
    sortable: true,
  },
  {
    key: "totalPatientCount",
    title: "累计患者数",
    width: 80,
    minWidth: 80,
    sortable: true,
  },
  {
    key: "newConsultPatientCount",
    title: "新增医疗患者数",
    width: 80,
    minWidth: 80,
    sortable: true,
  },
  {
    key: "newReConsultPatientCount",
    title: "新增复诊患者数",
    width: 80,
    minWidth: 80,
    sortable: true,
  },
  {
    key: "newPrescriptionCount",
    title: "新增方案数",
    width: 80,
    minWidth: 80,
    sortable: true,
  },
  {
    key: "newExecuteCount",
    title: "新增执行方案数",
    width: 80,
    minWidth: 80,
    sortable: true,
  },
  {
    key: "newPayCount",
    title: "新增付费方案数",
    width: 80,
    minWidth: 80,
    sortable: true,
  },
  {
    key: "newCiReCount",
    title: "新增执行磁疗方案数",
    width: 80,
    minWidth: 80,
    sortable: true,
  },
  {
    key: "newCiReUnitCount",
    title: "新增执行磁疗人天数",
    width: 80,
    minWidth: 80,
    sortable: true,
  },
  {
    key: "newGeWuCount",
    title: "新增执行隔物灸方案数",
    width: 80,
    minWidth: 80,
    sortable: true,
  },
  {
    key: "newGeWuUnitCount",
    title: "新增隔物灸贴数",
    sortable: true,
    width: 80,
    minWidth: 80,
  },
  {
    key: "newMaiZhenCount",
    title: "新增执行埋针方案数",
    sortable: true,
    width: 80,
    minWidth: 80,
  },
  {
    key: "newMaiZhenUnitCount",
    title: "新增埋针次数",
    sortable: true,
    width: 80,
    minWidth: 80,
  },
  {
    key: "newConsultCount",
    title: "新增问诊/咨询数",
    sortable: true,
    width: 80,
    minWidth: 80,
  },
  {
    key: "newPayConsultCount",
    title: "新增付费问诊/咨询数",
    sortable: true,
    width: 80,
    minWidth: 80,
  },
  {
    key: "profileDescription",
    title: "资料是否完整",
    width: 120,
    minWidth: 120,
  },
  {
    key: "enableConsult",
    title: "是否开启服务",
    sortable: true,
    width: 60,
    minWidth: 60,
  },
  {
    key: "showConsultCost",
    title: "服务价格",
    sortable: true,
    width: 70,
    minWidth: 70,
  },
  {
    key: "isLoginIn7Day",
    title: "本周是否登录",
    sortable: true,
    width: 60,
    minWidth: 60,
  },
  {
    key: "isTest",
    title: "是否测试数据",
    sortable: true,
    width: 80,
    minWidth: 80,
  },
  {
    key: "operations",
    title: "操作",
    width: 100,
    fixed: TableV2FixedDir.RIGHT,
    operations: [
      {
        label: "备注",
        onClick: (row) => onRemark(row),
      },
    ],
  },
];

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const { assistantIdList, deptIdList, orgIdList, ...parameters } = queryParams;

  const params = convertToRedashParams(parameters, "Report_DoctorStatistics");
  const r = await Report_Api.getRedashList<DoctorStatusRedash>(params);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  queryResultId = r.Data.QueryResultId;
  pageData.value = r.Data.Data;
  total.value = r.Data.TotalCount;
}

/** 查询结果Id */
let queryResultId = -1;

// 点击导出
async function onExport() {
  kEnableDebug && console.debug("点击导出");

  const { assistantIdList, deptIdList, orgIdList, ...parameters } = queryParams;
  const exportParams = convertToRedashParams(parameters, "Report_DoctorStatistics");
  const params: ExportTaskRedashDTO = {
    Cols: getExportTable2Columns(columns, "@"),
    ExecutingParams: exportParams.parameters,
    ExportWay: ExportEnum.PlainMySql,
    FileName: `医生情况统计-${Date.now()}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.pageIndex,
    PageSize: queryParams.pageSize,
    QueryName: "Report_DoctorStatistics",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
}

// 添加/编辑备注弹窗
const showDataDialog = reactive({
  isShow: false,
  data: {} as DoctorStatusRedash,
});

// 点击备注
function onRemark(row: DoctorStatusRedash) {
  kEnableDebug && console.debug("点击备注", row);

  if (!row.DocUserId) {
    ElMessage.error("医生Id为空");
    return;
  }

  showDataDialog.data = row;
  showDataDialog.isShow = true;
}

// 确认提交备注
function onConfirmSubmitItem() {
  kEnableDebug && console.debug("确认提交备注");

  showDataDialog.isShow = false;
  ElNotification.success("备注成功");

  queryParams.pageIndex = 1;
  requestTableList();
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
