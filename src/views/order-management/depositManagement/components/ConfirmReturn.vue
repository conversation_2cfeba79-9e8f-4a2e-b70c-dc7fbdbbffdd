<template>
  <div v-loading="pageLoading">
    <el-form
      v-if="!pageLoading"
      ref="formRef"
      :model="ruleForm"
      :rules="rules"
      label-width="100px"
      class="container-box-ruleForm"
    >
      <el-form-item label="订单编号" prop="OrderNo">
        <el-input v-model="ruleForm.OrderNo" disabled style="width: 180px" />
      </el-form-item>
      <el-form-item label="用户姓名" prop="UserName">
        <el-input v-model="ruleForm.UserName" style="width: 180px" />
      </el-form-item>
      <el-form-item v-if="showTakeDelivery" label="回收地址" prop="TakeDelivery">
        <el-input v-model="ruleForm.TakeDelivery" type="textarea" />
      </el-form-item>
      <el-form-item label="属地" prop="Market">
        <el-input v-model="ruleForm.Market" style="width: 180px" />
      </el-form-item>
      <el-form-item label="设备类型">
        <el-select
          v-if="ruleForm.DeviceDetails"
          v-model="ruleForm.DeviceDetails[0].DeviceType"
          placeholder="请选择设备类型"
          clearable
          @change="handleChangeDeviceType"
        >
          <el-option
            v-for="i in deviceList"
            :key="i.DeviceCode"
            :label="i.DeviceType"
            :value="i.DeviceType"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="设备编号" prop="DeviceTypeId">
        <el-input v-model="ruleForm.DeviceTypeId" disabled />
      </el-form-item>
      <el-form-item label="发货备注" prop="Remark">
        <el-input v-model="ruleForm.Remark" type="textarea" />
      </el-form-item>
      <el-form-item label="取货方式" prop="PickUpMethod">
        <el-select
          v-model="ruleForm.PickUpMethod"
          placeholder="请选择取货方式"
          @change="handlePickUpMethodChange"
        >
          <el-option label="医助/销售自行上门" value="医助/销售自行上门" />
          <el-option label="快递/同城上门" value="快递/同城上门" />
          <el-option label="患者送回医院" value="患者送回医院" />
          <el-option label="其他" value="其他" />
        </el-select>
      </el-form-item>
      <el-form-item label="回收仓" prop="ReturnToWarehouse">
        <el-select
          v-model="ruleForm.ReturnToWarehouse"
          filterable
          placeholder="请选择回收仓"
          :filter-method="filterWarehouse"
          @change="onChangeWarehouse"
        >
          <el-option
            v-for="(i, index) in filterWarehousesList"
            :key="index"
            :label="i.Warehouse"
            :value="i.Warehouse"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所在地" prop="WarehouseProbablyAddress">
        <el-input v-model="ruleForm.WarehouseProbablyAddress" disabled />
      </el-form-item>
      <el-form-item label="回收仓地址" prop="WarehouseDetailAddress">
        <el-select
          v-model="ruleForm.WarehouseDetailAddress"
          style="width: 600px"
          placeholder="请选择回收仓地址"
          filterable
        >
          <el-option
            v-for="(i, index) in warehouseDetailAddressList"
            :key="index"
            :label="i.Address"
            :value="i.Address"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="注意事项" prop="Note">
        <el-input
          v-model="ruleForm.Note"
          type="textarea"
          placeholder="请填写适合上门回收的时间段及相关注意事项"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { DeviceManageItem } from "@/api/consult/types";
import Supplier_Jiandao_Api from "@/api/supplier-jiandao";
import { FormInstance, FormRules } from "element-plus";
import { CreateRecoveryDeviceInputDTO } from "@/api/supplier-jiandao/types";

let ruleForm = ref<CreateRecoveryDeviceInputDTO>({
  OrderNo: "", //
  UserName: "", //
  TakeDelivery: "", //
  Market: "",
  DeviceTypeName: "",
  DeviceTypeId: "",
  Note: "",
  RecyclingTime: "",
  PickUpMethod: "",
  ReturnToWarehouse: "",
  WarehouseProbablyAddress: "",
  WarehouseDetailAddress: "",
  Remark: "",
  DeviceId: "",
  PrescriptionId: "",
  Org: "",
  RecyclingAddress: "",
  OrderDetailId: "",
  DeviceDetails: [
    {
      DeviceType: "",
      DeviceCode: "",
    },
  ],
});

const warehousesList = ref<WarehouseItem[]>([]);
const filterWarehousesList = ref<WarehouseItem[]>([]);
const showTakeDelivery = ref<boolean>(true);
const pageLoading = ref<boolean>(true);
const deviceList = ref<{ DeviceType: string; DeviceCode: string }[]>([]);
const warehouseDetailAddressList = ref<{ Address: string }[]>([]);
const formRef = ref<FormInstance>();

const rules = reactive<FormRules>({
  TakeDelivery: [{ required: true, message: "请输入回收地址", trigger: "blur" }],
  Market: [{ required: true, message: "请输入属地", trigger: "change" }],
  PickUpMethod: [{ required: true, message: "请选择取货方式", trigger: "blur" }],
  ReturnToWarehouse: [{ required: true, message: "请选择回收仓", trigger: "blur" }],
  WarehouseDetailAddress: [{ required: true, message: "请选择回收仓地址", trigger: "blur" }],
});

const filterWarehouse = (value: string) => {
  filterWarehousesList.value = warehousesList.value.filter((v) => v.Warehouse.includes(value));
};

const handleChangeDeviceType = (e: string) => {
  if (!e) {
    ruleForm.value.DeviceDetails[0].DeviceCode = "";
    return;
  }
  ruleForm.value.DeviceDetails[0].DeviceCode =
    deviceList.value.find((i: any) => i.DeviceType === e)?.DeviceCode ?? "";
};

const handlePickUpMethodChange = (e: string) => {
  if (e === "患者送回医院") {
    if (rules.TakeDelivery) {
      delete rules.TakeDelivery;
    }
    showTakeDelivery.value = false;
  } else {
    rules.TakeDelivery = [{ required: true, message: "请输入回收地址", trigger: "blur" }];
    showTakeDelivery.value = true;
  }
};

const onChangeWarehouse = (e: string) => {
  const item = warehousesList.value.filter((v) => v.Warehouse === e);
  ruleForm.value.WarehouseProbablyAddress = item[0].WarehouseAddress;
  ruleForm.value.WarehouseDetailAddress = item[0].Address;
  onGetWarehouseDetailAddressByWarehouse(e);
};

const onGetWarehouseDetailAddressByWarehouse = (e: string) => {
  const itemList = warehousesList.value.filter((v) => v.Warehouse === e);
  if (itemList && itemList.length > 0) {
    warehouseDetailAddressList.value = itemList;
  } else {
    warehouseDetailAddressList.value = [];
  }
};

const handleSubmit = async (): Promise<CreateRecoveryDeviceInputDTO | null> => {
  try {
    await formRef.value!.validate();
    ruleForm.value.RecyclingAddress = ruleForm.value.TakeDelivery;
    return ruleForm.value;
  } catch {
    return null;
  }
};

const handleProcessingData = async (info: DeviceManageItem) => {
  if (!info.OrderNo) return;
  // 将info中的部分数据存储到ruleForm中去
  handleSetRuleForm(info);
  // 获取用户相关的数据
  await onGetUserData(info.OrderNo);
  pageLoading.value = false;
  // 获取回收仓
  onGetWarehouses();
};

const handleSetRuleForm = (info: DeviceManageItem) => {
  if (info.DeviceOrderInfo.OrderDetails && info.DeviceOrderInfo.OrderDetails.length > 0) {
    const itemList = info.DeviceOrderInfo.OrderDetails.filter(
      (item) => item.RelationId === info.DeviceId
    );
    if (itemList && itemList.length > 0) {
      ruleForm.value.OrderDetailId = itemList[0].Id;
    }
  }
  ruleForm.value.DeviceId = info.DeviceId;
  ruleForm.value.PrescriptionId = info.PrescriptionId;
  ruleForm.value.Org = info.OrganizationName;
  ruleForm.value.RecyclingTime = dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
};

const onGetWarehouses = async () => {
  const res = await Supplier_Jiandao_Api.getWarehouses();
  if (res.Type === 200 && res.Data.length) {
    warehousesList.value = res.Data;
  }
};

const onGetUserData = async (orderNo: string) => {
  const res = await Supplier_Jiandao_Api.getOutGoodsByOrderNo({
    orderNo,
  });
  if (res.Type !== 200) {
    ElMessage.error(res.Content);
    return;
  }
  if (!res.Data.DeviceDetails || !res.Data.DeviceDetails.length) {
    ElMessage.error("未查询到发货单中待退还设备信息，请检查简道云发货单信息是否完整");
  } else {
    deviceList.value = res.Data.DeviceDetails;
    ruleForm.value.DeviceDetails = JSON.parse(JSON.stringify(res.Data.DeviceDetails));
  }
  ruleForm.value.OrderNo = res.Data.OrderNo;
  ruleForm.value.UserName = res.Data.UserName;
  ruleForm.value.TakeDelivery = res.Data.TakeDelivery;
  ruleForm.value.Market = res.Data.Market;
};

interface Props {
  detailInfo: DeviceManageItem | null;
}
const props = defineProps<Props>();

watch(
  () => props.detailInfo,
  (newVal) => {
    if (newVal) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>
