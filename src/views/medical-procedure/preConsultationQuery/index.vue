<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  :shortcuts="datePickerShortcuts"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="机构" prop="OrgId">
                <HospitalSelect v-model="queryParams.OrgId" />
              </el-form-item>
              <el-form-item label="医生" prop="DoctorId">
                <UserSelect
                  v-model="queryParams.DoctorId"
                  :org-ids="queryParams.OrgId ? [queryParams.OrgId] : null"
                  :role-types="['doctor']"
                />
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="患者名字/手机号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="UserName" label="姓名" align="center" />
          <el-table-column prop="Sex" label="性别" align="center" />
          <el-table-column label="年龄" align="center">
            <template #default="scope">
              <span v-if="scope.row.Birthday">
                {{ Math.abs(dayjs().diff(dayjs(scope.row.Birthday), "year")) }}
              </span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column prop="PhoneNumber" label="手机号" align="center" />
          <el-table-column
            prop="CreatedTime"
            label="就诊时间"
            align="center"
            :formatter="tableDateFormat"
          />
          <el-table-column prop="OrgName" label="来源医院" align="center" />
          <el-table-column prop="DoctorName" label="来源医生" align="center" />
          <el-table-column prop="State" label="状态" align="center" />
          <el-table-column fixed="right" label="操作" width="80" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog
      v-model="showPreApplyRecordAiResults"
      title="预问诊推荐"
      width="600"
      destroy-on-close
      :close-on-click-modal="true"
      :close-on-press-escape="true"
    >
      <PreApplyRecordAiResults :params="preApplyRecordParams" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import {
  LatestPreVisitAiAnalysisInputDTO,
  PreVisitRecordInputDTO,
  PreVisitRecordItem,
} from "@/api/consult/types";
import Consult_Api from "@/api/consult";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();

defineOptions({
  name: "PreConsultationQuery",
});

const queryParams = ref<PreVisitRecordInputDTO>({
  DoctorId: null,
  OrgId: null,
  BeginTime: dayjs().format("YYYY-MM-01 00:00:00"),
  EndTime: dayjs().format("YYYY-MM-DD 23:59:59"),
  PageIndex: 1,
  Type: 1, // 1: 预问诊 0: 诊后报道码
  PageSize: 20,
  Keyword: null,
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const showPreApplyRecordAiResults = ref<boolean>(false);
const preApplyRecordParams = ref<LatestPreVisitAiAnalysisInputDTO>({
  DoctorId: "",
  PatientId: "",
  CreatedTime: "",
});

const { tableLoading, pageData, total, tableRef, tableDateFormat, tableFluidHeight, tableResize } =
  useTableConfig<PreVisitRecordItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handlePreviewOrEdit = async (row: PreVisitRecordItem) => {
  preApplyRecordParams.value.DoctorId = row.DoctorId!;
  preApplyRecordParams.value.PatientId = row.PatientId!;
  preApplyRecordParams.value.CreatedTime = dayjs(row.CreatedTime).format("YYYY-MM-DD");
  showPreApplyRecordAiResults.value = true;
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Consult_Api.getPreVisitRecordList(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};

watch(timeRange, (newVal) => {
  queryParams.value.BeginTime = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndTime = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
