<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="机构">
                <HospitalSelect
                  v-model="queryParams.OrgId"
                  :scopeable="true"
                  @change="
                    () => {
                      queryParams.RelatedId = undefined;
                      requestEvaluateGaugeList();
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="随访问卷">
                <KSelect
                  v-model="queryParams.RelatedId"
                  :data="gaugeList"
                  :props="{ label: 'Name', value: 'Id' }"
                  placeholder="请输入关键字进行筛选"
                  :loading="gaugeLoading"
                  :disabled="!queryParams.OrgId"
                  :show-all="true"
                  filterable
                />
              </el-form-item>
              <el-form-item label="随访日期">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  unlink-panels
                  class="w-300px!"
                />
              </el-form-item>
              <el-form-item label="随访方式">
                <KSelect
                  v-model="queryParams.PlanType"
                  :data="[
                    { label: '电话随访', value: 1 },
                    { label: '在线随访', value: 2 },
                    { label: '来院', value: 3 },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="关键字">
                <el-input
                  v-model="queryParams.KeyWords"
                  placeholder="患者姓名/编号"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              :disabled="pageData.length === 0 || !queryParams.RelatedId"
              :loading="exportLoading"
              @click="onExport"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="Id"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
        >
          <el-table-column prop="PatNo" label="患者编号" min-width="100" show-overflow-tooltip />
          <el-table-column prop="PatName" label="姓名" min-width="80" show-overflow-tooltip />
          <el-table-column prop="Sex" label="性别" width="60" />
          <el-table-column prop="PlanType" label="随访方式" width="90">
            <template #default="scope">
              {{ ["未知", "电话随访", "在线随访", "来院"][scope.row.PlanType] }}
            </template>
          </el-table-column>
          <el-table-column prop="ExecName" label="执行人" min-width="80" show-overflow-tooltip />
          <el-table-column
            prop="RelatedName"
            label="随访问卷"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column prop="Remark" label="备注" min-width="100" show-overflow-tooltip />
          <el-table-column
            label="随访日期"
            prop="StartTime"
            width="100"
            :formatter="tableDateFormatDay"
          />
          <el-table-column label="状态" width="80">
            <template #default="scope">
              {{ ["未知", "已完成"][scope.row.State] }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="100">
            <template #default="scope">
              <el-button link type="primary" @click="onPreviewDetail(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 查看 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    :title="showDataDialog.title"
    width="850"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <FollowUpDetail :data="showDataDialog.data" @cancel="showDataDialog.isShow = false" />
  </el-dialog>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { GetFollowUpPlanDetailPageParams } from "@/api/consult/types";
import Consult_Api from "@/api/consult";
import Training_Api from "@/api/training";
import { GetEvaluateGaugeInputDTO } from "@/api/training/types";
import { exportExcel } from "@/utils/serviceUtils";
import { ExportTaskRedashDTO } from "@/api/report/types";
import { ExportEnum } from "@/enums/Other";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";
import FollowUpDetail from "./components/FollowUpDetail.vue";

const { datePickerShortcuts } = useDateRangePicker();

interface QueryParams extends Omit<GetFollowUpPlanDetailPageParams, "PlanType"> {
  PlanType?: number;
}

// 调试开关
const kEnableDebug = false;
defineOptions({
  name: "FollowUpResult",
});

const {
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  tableDateFormatDay,
  exportLoading,
} = useTableConfig<FollowUpPlan>();

// 随访问卷列表
const gaugeList = ref<BaseGauge[]>([]);
const gaugeLoading = ref(false);

// 查询条件
const queryParams = reactive<QueryParams>({
  StartTime: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
  EndTime: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  DetailType: 3,
  State: 1,
  PageIndex: 1,
  PageSize: 20,
});

// 定义 dateRange
const dateRange = computed({
  get() {
    // 从 queryParams 中获取日期范围
    return [queryParams.StartTime, queryParams.EndTime];
  },
  set(newValue) {
    // 当用户选择日期范围时，更新 queryParams
    if (newValue && newValue.length === 2) {
      queryParams.StartTime = newValue[0].split(" ")[0] + " 00:00:00";
      queryParams.EndTime = newValue[1].split(" ")[0] + " 23:59:59";
    }
  },
});

// 查看弹窗
const showDataDialog = reactive({
  isShow: false,
  title: "查看随访",
  data: {} as FollowUpPlan, // 查看详情
});

// 点击搜索
function handleQuery() {
  queryParams.PageIndex = 1;
  requestTableList();
}

// 点击导出
async function onExport() {
  kEnableDebug && console.debug("点击导出");

  const params: ExportTaskRedashDTO = {
    ExecutingParams: {
      StartTime: queryParams.StartTime,
      EndTime: queryParams.EndTime,
      KeyWords: queryParams.KeyWords,
      PlanType: queryParams.PlanType,
      OrgId: queryParams.OrgId,
      State: queryParams.State,
      RelatedId: queryParams.RelatedId,
      DetailType: 3,
    },
    ExportWay: ExportEnum.ServiceInvoke,
    FileName: `随访结果-${Date.now()}.xlsx`,
    ServiceExportCode: "FollowUpPlanDetailReport",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
}

// 点击查看
async function onPreviewDetail(row?: FollowUpPlan) {
  kEnableDebug && console.debug("查看/编辑", row);

  if (!row?.FollowUpPlanId) {
    ElMessage.error("随访计划ID为空");
    return;
  }

  showDataDialog.data = row;
  showDataDialog.isShow = true;
}

// 请求随访问卷列表
async function requestEvaluateGaugeList() {
  gaugeLoading.value = true;
  const params: GetEvaluateGaugeInputDTO = {
    IsEnble: true,
    Type: 1,
    OrganizationId: queryParams.OrgId,
    PageIndex: 1,
    PageSize: 999,
  };
  const r = await Training_Api.getEvaluateGaugePage(params);
  gaugeLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  gaugeList.value = r.Data.Rows;
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const params: GetFollowUpPlanDetailPageParams = {
    ...queryParams,
    PlanType: queryParams.PlanType ? [queryParams.PlanType] : [1, 2, 3],
  };
  const r = await Consult_Api.getFollowUpPlanDetailPage(params);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  pageData.value = r.Data.Data;
  total.value = r.Data.TotalCount;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
