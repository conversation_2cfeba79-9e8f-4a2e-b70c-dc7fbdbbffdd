<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="推荐时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  :shortcuts="datePickerShortcuts"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="推荐方式" prop="Source">
                <el-select
                  v-model="queryParams.Source"
                  placeholder="请选择推荐方式"
                  style="width: 120px"
                  clearable
                  :empty-values="['', null, undefined]"
                  :value-on-clear="() => null"
                >
                  <el-option label="扫码推荐" :value="1" />
                  <el-option label="预问诊推荐" :value="2" />
                  <el-option label="诊后报道推荐" :value="3" />
                </el-select>
              </el-form-item>
              <el-form-item label="医院" prop="OrgIds">
                <HospitalSelect
                  v-model="queryParams.OrgIds"
                  multiple
                  @change="queryParams.DeptId = null"
                />
              </el-form-item>
              <el-form-item label="科室" prop="DeptIds">
                <DeptSelect
                  v-model="queryParams.DeptId"
                  :org-id="queryParams.OrgIds && queryParams.OrgIds[0]"
                  :disabled="
                    !queryParams.OrgIds ||
                    queryParams.OrgIds.length > 1 ||
                    !queryParams.OrgIds.length
                  "
                />
              </el-form-item>
              <el-form-item label="医生" prop="DctIds">
                <UserSelect
                  v-model="queryParams.DctIds"
                  :role-types="['doctor']"
                  multiple
                  :org-ids="queryParams.OrgIds"
                  :dept-ids="queryParams.DeptId ? [queryParams.DeptId] : null"
                />
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="患者名字/手机号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="UserName" label="姓名" align="center" width="100" />
          <el-table-column prop="Sex" label="性别" align="center" width="80" />
          <el-table-column prop="Age" label="年龄" align="center" width="80" />
          <el-table-column prop="UserPhone" label="手机号" align="center" />
          <el-table-column
            prop="CreatedTime"
            label="推荐时间"
            align="center"
            :formatter="tableDateFormat"
          />
          <el-table-column prop="OrgName" label="推荐方式" width="200" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.Source === 1" type="info">
                扫码推荐
                {{ scope.row.RecommendName ? ` - ${scope.row.RecommendName}` : "" }}
              </el-tag>
              <el-tag v-else-if="scope.row.Source === 2" type="info">预问诊推荐</el-tag>
              <el-tag v-else-if="scope.row.Source === 3" type="info">扫码报道</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="OrgName" label="医院" align="center" />
          <el-table-column prop="DeptName" label="科室" align="center" />
          <el-table-column prop="DctName" label="推荐医生" align="center" />
          <el-table-column fixed="right" label="操作" width="80" align="center">
            <template #default="scope">
              <el-button
                v-if="scope.row.Source === 2"
                link
                type="primary"
                @click="handlePreview(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog
      v-model="showPreApplyRecordAiResults"
      title="预问诊推荐"
      width="600"
      destroy-on-close
      :close-on-click-modal="true"
      :close-on-press-escape="true"
    >
      <PreApplyRecordAiResults :params="preApplyRecordParams" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import Consult_Api from "@/api/consult";
import {
  LatestPreVisitAiAnalysisInputDTO,
  PreApplyRecordInputDTO,
  PreApplyRecordItem,
} from "@/api/consult/types";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();

defineOptions({
  name: "DoctorRecommendRecord",
});

const queryParams = ref<PreApplyRecordInputDTO>({
  Source: null, //1为患者自己扫码  2为预问诊推荐
  Keyword: null, //患者名字，手机号
  DeptId: null,
  DctIds: null,
  StartDate: dayjs(new Date()).format("YYYY-MM-01 00:00:00"),
  EndDate: dayjs(new Date()).format("YYYY-MM-DD 23:59:59"),
  PageIndex: 1,
  PageSize: 20,
  OrgIds: null,
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const showPreApplyRecordAiResults = ref<boolean>(false);
const preApplyRecordParams = ref<LatestPreVisitAiAnalysisInputDTO>({
  DoctorId: "",
  PatientId: "",
  CreatedTime: "",
});
const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const { tableLoading, pageData, total, tableRef, tableDateFormat, tableFluidHeight, tableResize } =
  useTableConfig<PreApplyRecordItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handlePreview = async (row: PreApplyRecordItem) => {
  preApplyRecordParams.value.DoctorId = row.DctId || "";
  preApplyRecordParams.value.PatientId = row.UserId || "";
  preApplyRecordParams.value.CreatedTime = dayjs(row.CreatedTime).format("YYYY-MM-DD");
  showPreApplyRecordAiResults.value = true;
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Consult_Api.getPreApplyRecord(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};

watch(timeRange, (newVal) => {
  queryParams.value.StartDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
