<template>
  <div class="h-[calc(100vh-340px)] flex">
    <!-- 左侧列表 -->
    <div class="w-1/5 border-r border-gray-100 overflow-y-auto bg-white">
      <el-scrollbar class="h-full">
        <div class="gauge-list">
          <div
            v-for="item in patGaugeList"
            :key="item.Id"
            :class="[
              'gauge-list-item hover:bg-gray-50 px-6 py-3 cursor-pointer transition-all duration-300',
              { 'bg-blue-50 text-blue-500': currentGauge?.Id === item.Id },
            ]"
            @click="handleSelectGauge(item.Id!)"
          >
            {{ formatTime(item.CreatedTime!) }}
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 右侧内容 -->
    <div v-if="currentGauge" class="flex-1 px-6 overflow-y-auto bg-white">
      <!-- 量表信息 -->
      <div class="text-center border-b border-gray-100">
        <h2 class="text-xl font-medium mb-3">
          {{ currentGauge.Name }}
        </h2>
        <div>
          量表总分：
          <span class="text-red-500 text-base font-medium">
            {{ currentGauge.SumPoint }}
          </span>
        </div>
      </div>

      <!-- 题目列表 -->
      <el-card shadow="never" class="card-content">
        <div
          v-for="(problem, index) in currentGauge.PatGaugeProblems"
          :key="problem.Title"
          class="mb-6"
        >
          <div class="mb-4">
            <span class="mr-2">{{ index + 1 }}.</span>
            <span>{{ problem.Title }}</span>
            <span v-if="problem.IsRequired" class="text-red-500 ml-1">•</span>
          </div>
          <div class="ml-6">
            <!-- 单选题 -->
            <el-radio-group
              v-if="problem.ProblemType === 1"
              v-model="problemAnswers[index + '']"
              disabled
            >
              <el-radio
                v-for="detail in problem.PatGaugeProblemDetails"
                :value="detail.Answer"
                :label="detail.Answer"
              >
                {{ detail.ProblemOption }}
              </el-radio>
            </el-radio-group>

            <!-- 多选题 -->
            <el-checkbox-group
              v-else-if="problem.ProblemType === 2"
              v-model="problemAnswers[index + '']"
              disabled
            >
              <el-checkbox
                v-for="detail in problem.PatGaugeProblemDetails"
                :value="detail.Answer"
                :label="detail.ProblemOption"
              >
                {{ detail.ProblemOption }}
              </el-checkbox>
            </el-checkbox-group>

            <!-- 填空题 -->
            <el-input
              v-else-if="problem.ProblemType === 3"
              v-model="problemAnswers[index + '']"
              type="textarea"
              :rows="2"
              disabled
            />

            <!-- 分值题 -->
            <el-input-number
              v-else-if="problem.ProblemType === 4"
              v-model="problemAnswers[index + '']"
              :min="0"
              :controls="false"
              disabled
              class="w-30"
            />
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import Training_Api from "@/api/training";
import dayjs from "dayjs";

const props = defineProps<{
  patGaugeId: string;
}>();

const patGaugeList = ref<DctPatGauge[]>([]);
const currentGauge = ref<DctPatGauge | null>(null);
const problemAnswers = reactive<Record<string, any>>({});

// 格式化时间
const formatTime = (time: string) => {
  return dayjs(time).format("YYYY-MM-DD HH:mm");
};

// 获取量表数据
const handleGetPatGaugeById = async () => {
  try {
    const res = await Training_Api.dctGetPatGaugeById({ patGaugeId: props.patGaugeId });
    if (res.Type === 200) {
      patGaugeList.value = res.Data || [];
    }
  } catch (error) {
    console.log(error);
  }
};

// 选择量表
const handleSelectGauge = (gaugeId: string) => {
  const gauge = patGaugeList.value.find((item) => item.Id === gaugeId);
  if (gauge) {
    currentGauge.value = gauge;
  }
};

// 初始化问题答案
const initProblemAnswers = (gauge: DctPatGauge) => {
  gauge.PatGaugeProblems?.forEach((problem, index) => {
    // 获取问题详细信息
    const problemDetails = problem.PatGaugeProblemDetails;
    if (!problemDetails) return;

    // 根据问题类型处理答案
    const getAnswer = () => {
      switch (problem.ProblemType) {
        case 1:
          // 单选
          return problemDetails.find((d) => d.Answer === "1")?.Answer || "";
        case 2:
          // 多选
          return problemDetails.filter((d) => d.Answer === "1").map((d) => d.Answer);
        case 3:
        case 4:
          // 填空或分值
          return problemDetails[0]?.Answer || "";
        default:
          return undefined;
      }
    };

    const answer = getAnswer();
    if (answer !== undefined) {
      problemAnswers[index + ""] = answer;
    }
  });
};

// 监听属性变化
watch(
  () => props.patGaugeId,
  () => {
    handleGetPatGaugeById();
  },
  { immediate: true }
);

// 监听量表列表变化，设置默认选中的量表
watch(
  patGaugeList,
  (newList) => {
    if (newList.length > 0 && !currentGauge.value) {
      handleSelectGauge(newList[0].Id!);
    }
  },
  { immediate: true }
);

// 监听当前量表变化，更新问题答案
watch(
  currentGauge,
  (newGauge) => {
    if (newGauge) {
      initProblemAnswers(newGauge);
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.card-content :deep(.el-card__body) {
  padding: 20px;
}
</style>
