<template>
  <div class="p-20px overflow-y-auto h-600px">
    <div class="flex items-center justify-start">
      <el-input
        v-model="queryParams.keywords"
        class="w-250px!"
        type="text"
        placeholder="请输入患者姓名/手机号"
        @keyup.enter="handleQuery"
      />
      <el-button type="primary" class="ml-10px mr-20px" @click="handleQuery">搜索</el-button>
      <el-text>目前只会展示10个，若没有找到，请搜索选择</el-text>
    </div>
    <el-table
      v-loading="tableLoading"
      class="mt-20px"
      :data="pageData"
      border
      :header-cell-style="{ textAlign: 'center' }"
      :cell-style="{ textAlign: 'center' }"
      highlight-current-row
    >
      <el-table-column prop="Name" label="姓名" />
      <el-table-column prop="Sex" label="性别" />
      <el-table-column prop="Age" label="年龄" />
      <el-table-column prop="PhoneNumber" label="手机号" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button link type="primary" @click="onSelectItem(scope.row)">选择</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import { PatientInquiryParams, PatientRedash } from "@/api/report/types";
import { useUserStore } from "@/store";
import Report_Api from "@/api/report";
import { convertToRedashParams } from "@/utils/serviceUtils";

// 调试开关
const kEnableDebug = false;
defineOptions({
  name: "PatientSelect",
});

const props = defineProps<{
  organizationId: string;
}>();

const emit = defineEmits<{
  cancel: [];
  select: [PatientRedash];
}>();

const { pageData, tableLoading } = useTableConfig<PatientRedash>();

// 查询条件
const queryParams = reactive<RedashParameters<PatientInquiryParams>>({
  departmentId: "*",
  doctorUserId: "*",
  keywords: undefined,
  tagKeyWords: ".",
  isTest: "*",
  orgIds: props.organizationId ?? "*",
  LoginUserId: useUserStore().userInfo.Id,
  pageIndex: 1,
  pageSize: 20,
});

// 点击搜索
function handleQuery() {
  queryParams.pageIndex = 1;
  requestTableList();
}

// 点击选择
async function onSelectItem(row?: PatientRedash) {
  kEnableDebug && console.debug("选择患者", row);
  if (row) {
    emit("select", row);
  }
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const params = convertToRedashParams(queryParams, "Report_PatientsDetail");
  const r = await Report_Api.getRedashList<PatientRedash>(params);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  pageData.value = r.Data.Data;
}

onMounted(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
