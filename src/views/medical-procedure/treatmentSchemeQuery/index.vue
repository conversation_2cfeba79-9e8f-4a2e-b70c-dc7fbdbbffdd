<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer is-show-toggle>
          <template #left>
            <el-form ref="queryFormRef" label-position="right" :model="queryParams" :inline="true">
              <el-form-item label="方案状态" prop="States">
                <el-select
                  v-model="queryParams.States"
                  clearable
                  multiple
                  collapse-tags
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="(item, index) in prescriptionStateData"
                    :key="index"
                    :label="item.Name"
                    :value="item.Id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item prop="TimeType">
                <el-select v-model="queryParams.TimeType" placeholder="请选择">
                  <el-option label="开具时间" :value="1" />
                  <el-option label="执行时间" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item label="时间段" prop="DateRange">
                <el-date-picker
                  v-model="queryParams.DateRange"
                  unlink-panels
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @clear="queryParams.DateRange = ['2020-01-01', '2099-01-01']"
                />
              </el-form-item>
              <el-form-item label="是否测试数据" prop="IsTest">
                <el-select
                  v-model="queryParams.IsTest"
                  placeholder="请选择"
                  style="width: 100px"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否续方" prop="IsContinue">
                <el-select
                  v-model="queryParams.IsContinue"
                  placeholder="请选择"
                  style="width: 100px"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" :value="1" />
                  <el-option label="否" :value="0" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否自主开方" prop="SelfReliance">
                <el-select
                  v-model="queryParams.SelfReliance"
                  placeholder="请选择"
                  style="width: 100px"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否已创建订单" prop="IsExecut">
                <el-select
                  v-model="queryParams.IsExecut"
                  placeholder="请选择"
                  style="width: 100px"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="有订单" :value="true" />
                  <el-option label="无订单" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item label="开方医院" prop="OrgIds">
                <HospitalSelect ref="hospitalSelectRef" v-model="queryParams.OrgIds" multiple />
              </el-form-item>
              <el-form-item label="科室" prop="DeptIds">
                <DeptSelect
                  v-model="queryParams.DeptIds"
                  multiple
                  :org-id="(queryParams.OrgIds && (queryParams.OrgIds as string[])[0]) || null"
                  :disabled="!queryParams.OrgIds || (queryParams.OrgIds as string[]).length !== 1"
                />
              </el-form-item>
              <el-form-item label="下达方式" prop="Sources">
                <el-select
                  v-model="queryParams.Sources"
                  placeholder="请选择"
                  clearable
                  multiple
                  collapse-tags
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="问诊咨询" :value="1" />
                  <el-option label="主动下达" :value="2" />
                  <el-option label="快速开方" :value="3" />
                </el-select>
              </el-form-item>
              <el-form-item label="下达人角色" prop="Role">
                <el-select
                  v-model="queryParams.Role"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="医生" value="doctor" />
                  <el-option label="治疗师" value="therapist" />
                  <el-option label="护士" value="nurse" />
                </el-select>
              </el-form-item>
              <el-form-item label="下达人" prop="CreatorIds">
                <UserSelect
                  ref="userSelectRef"
                  v-model="queryParams.CreatorIds"
                  multiple
                  :org-ids="queryParams.OrgIds"
                  :dept-ids="queryParams.DeptIds"
                  :role-types="
                    queryParams.Role ? [queryParams.Role] : ['doctor', 'therapist', 'nurse']
                  "
                />
              </el-form-item>
              <el-form-item label="医助" prop="AssistantId">
                <UserSelect v-model="queryParams.AssistantId" :role-types="['assistant']" />
              </el-form-item>
              <el-form-item label="医嘱" prop="MoName">
                <el-input
                  v-model.trim="queryParams.MoName"
                  type="text"
                  placeholder="输入医嘱名称"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="治疗费用">
                <el-input-number
                  v-model="queryParams.StartAmount"
                  :min="0"
                  placeholder="起始金额"
                />
                <span class="mx-10px">-</span>
                <el-input-number
                  v-model="queryParams.EndAmount"
                  :min="queryParams.StartAmount || 0"
                  placeholder="结束金额"
                />
              </el-form-item>
              <el-form-item label="关键字">
                <el-input
                  v-model="queryParams.Keyword"
                  type="text"
                  placeholder="方案编号/就诊人姓名/手机号/订单编号"
                  style="width: 280px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              v-hasNoPermission="['externalSeller']"
              type="primary"
              :disabled="!selectedTableIds.length"
              :loading="changeTestLoading"
              @click="handleChangeIsTest"
            >
              切换是否测试数据
            </el-button>
            <el-button
              v-hasNoPermission="['externalSeller']"
              type="primary"
              :disabled="pageData.length <= 0"
              :loading="exportLoading"
              @click="handleExportExcel"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          :height="tableFluidHeight"
          row-key="PrescriptionId"
          align="center"
          highlight-current-row
          style="text-align: center; flex: 1"
          @select="handleTableSelect"
          @select-all="handleTableSelect"
        >
          <el-table-column type="selection" reserve-selection width="55" />
          <el-table-column prop="PrescriptionId" label="方案编号" width="180" align="center" />
          <el-table-column label="是否测试数据" width="100" align="center">
            <template #default="scope">
              {{ scope.row.IsTest === 1 ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column label="下达方式" width="100" align="center">
            <template #default="scope">
              {{ ["", "问诊咨询", "主动下达", "快速开方"][scope.row.Source] }}
            </template>
          </el-table-column>
          <el-table-column label="角色" width="100" align="center">
            <template #default="scope">
              {{
                scope.row.Role === "doctor"
                  ? "医生"
                  : scope.row.Role === "therapist"
                    ? "治疗师"
                    : "护士"
              }}
            </template>
          </el-table-column>
          <el-table-column prop="UserName" label="就诊人" width="180" align="center">
            <template #default="scope">
              <p>{{ scope.row.UserName }} {{ scope.row.Sex }} {{ scope.row.Age }}</p>
              <p v-if="scope.row.PhoneNumber">手机号：{{ scope.row.PhoneNumber }}</p>
            </template>
          </el-table-column>
          <el-table-column prop="SendTime" label="开具时间" width="180" align="center" />
          <el-table-column prop="OrganizationName" label="开方医院" width="180" align="center" />
          <el-table-column prop="DeptName" label="科室" width="150" align="center" />
          <el-table-column prop="CreatorName" label="下达人" width="100" align="center" />
          <el-table-column prop="Mentor" label="指导人" width="100" align="center" />
          <el-table-column prop="AssistantName" label="医助" width="100" align="center" />
          <el-table-column prop="DiagnoseName" label="诊断" width="180" align="center" />
          <el-table-column label="是否查看" width="80" align="center">
            <template #default="scope">
              {{ scope.row.PatCheckTime ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column label="是否续方" width="80" align="center">
            <template #default="scope">
              {{ ["否", "是"][scope.row.IsContinue] }}
            </template>
          </el-table-column>
          <el-table-column label="自主开方" width="100" align="center">
            <template #default="scope">
              <el-switch
                v-model="scope.row.SelfReliance"
                :disabled="
                  !userStore.userInfo.Roles ||
                  !(userStore.userInfo.Roles as string[]).some((v) => v === 'superAdmin')
                "
                @change="
                  (event) => handleSelfRelianceChange(scope.row.PrescriptionId, event as boolean)
                "
              />
            </template>
          </el-table-column>
          <el-table-column prop="PayTime" label="执行时间" width="180" align="center" />
          <el-table-column
            prop="TotalAmount"
            label="治疗费用"
            :show-overflow-tooltip="true"
            width="80"
            align="center"
          />
          <el-table-column
            label="医嘱明细"
            :show-overflow-tooltip="true"
            align="center"
            width="180"
          >
            <template #default="scope">
              <p v-for="(i, index) in handleGetDetailList(scope.row.Detail)" :key="index">
                {{ i }}
              </p>
            </template>
          </el-table-column>
          <el-table-column prop="OrderNo" label="订单编号" width="180" align="center" />
          <el-table-column label="状态" width="100" align="center">
            <template #default="scope">
              {{ getExecutStateName(scope.row.ExecutState) }}
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { PrescriptionState } from "@/enums/PrescriptionEnum";
import { useTableConfig } from "@/hooks/useTableConfig";
import { GetPrescriptonExecutInputDTO, PrescriptonExecut } from "@/api/consult/types";
import Consult_Api from "@/api/consult";
import Report_Api from "@/api/report";
import { EventBus } from "@/utils/eventBus";
import { useUserStore } from "@/store";
import UserSelect from "@/components/UserSelect/index.vue";
import HospitalSelect from "@/components/HospitalSelect/index.vue";
import { ExportEnum } from "@/enums/Other";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<PrescriptonExecut>();

defineOptions({
  name: "TreatmentSchemeQuery",
  inheritAttrs: false,
});
interface PageGetPrescriptonExecutInputDTO extends GetPrescriptonExecutInputDTO {
  DateRange?: [string, string];
}
const changeTestLoading = ref<boolean>(false);
const exportLoading = ref<boolean>(false);
const selectedTableIds = ref<string[]>([]);
const userSelectRef = ref<InstanceType<typeof UserSelect>>();
const hospitalSelectRef = ref<InstanceType<typeof HospitalSelect>>();
const queryParams = ref<PageGetPrescriptonExecutInputDTO>({
  States: null,
  TimeType: 1,
  BeginTime: "",
  EndTime: "",
  DateRange: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
  IsTest: false,
  IsContinue: null,
  SelfReliance: null,
  IsExecut: null,
  OrgIds: null,
  DeptIds: null,
  Sources: null,
  Role: null,
  CreatorIds: null,
  AssistantId: null,
  MoName: "",
  StartAmount: null,
  EndAmount: null,
  Keyword: "",
  PageIndex: 1,
  PageSize: 20,
});
const prescriptionStateData = ref<{ Name: string; Id: number }[]>([
  { Name: "待确认", Id: PrescriptionState.PendingConfirm },
  { Name: "未执行", Id: PrescriptionState.PendingExecution },
  { Name: "已执行", Id: PrescriptionState.Executed },
  { Name: "已失效", Id: PrescriptionState.Expired },
  { Name: "待生效", Id: PrescriptionState.PendingActivation },
]);
const userStore = useUserStore();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};
const handleGetTableList = async () => {
  queryParams.value.BeginTime = dayjs(queryParams.value.DateRange![0]).format(
    "YYYY-MM-DD 00:00:00"
  );
  queryParams.value.EndTime = dayjs(queryParams.value.DateRange![1]).format("YYYY-MM-DD 23:59:59");
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  Object.keys(copyData).forEach((key) => {
    if (copyData[key] === null) {
      delete copyData[key];
    }
  });
  delete copyData.DateRange;
  tableLoading.value = true;
  const res = await Consult_Api.getPrescriptonExecut(copyData);
  if (res.Type === 200) {
    res.Data.Data.forEach((s) => {
      if (s.SendTime) s.SendTime = dayjs(s.SendTime).format("YYYY-MM-DD HH:mm:ss");
      if (s.PayTime) s.PayTime = dayjs(s.PayTime).format("YYYY-MM-DD HH:mm:ss");
    });
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};
const handleGetDetailList = (detail: string): string[] => {
  if (!detail) return [];
  return detail.split("\n");
};
const getExecutStateName = (Id: number): string => {
  return prescriptionStateData.value.filter((v) => v.Id === Id)[0].Name;
};
const handleSelfRelianceChange = (id: string, event: boolean) => {
  Consult_Api.batchOperaPre({
    PreIds: [id],
    SelfReliance: event,
  })
    .then((res) => {
      if (res.Type !== 200) {
        ElNotification.warning({
          title: "提示",
          message: res.Content,
        });
        handleGetTableList();
        return;
      }
      ElNotification.success({
        title: "提示",
        message: res.Content,
      });
    })
    .catch(() => {
      handleGetTableList();
    });
};
const handleTableSelect = (selection: PrescriptonExecut[]) => {
  selectedTableIds.value = selection.map((item) => item.PrescriptionId);
};
const handleChangeIsTest = () => {
  if (!selectedTableIds.value.length) {
    ElMessage.warning("请选择治疗方案");
    return;
  }
  changeTestLoading.value = true;
  Consult_Api.switchTestStatus(selectedTableIds.value)
    .then((res) => {
      if (res.Type !== 200) {
        ElNotification.warning({
          title: "提示",
          message: res.Content,
        });
        return;
      }
      ElNotification.success({
        title: "提示",
        message: res.Content,
      });
      selectedTableIds.value = [];
      tableRef.value?.clearSelection();
      handleGetTableList();
    })
    .catch(() => {
      ElNotification.warning({
        title: "提示",
        message: "操作失败",
      });
    })
    .finally(() => {
      changeTestLoading.value = false;
    });
};
const handleExportExcel = async () => {
  const params = handleGetQueryParams();
  const newData = {
    ServiceExportCode: "PrescriptionExecReport", //后端
    ExportWay: ExportEnum.ServiceInvoke, // O:Redash, 1：后端
    ExecutingParams: params,
    FileName: `治疗方案查询-${Date.now()}.xlsx`,
  };
  exportLoading.value = true;
  const res = await Report_Api.createExportTask(newData);
  if (res.Type === 200) {
    const notify = ElNotification.success({
      message: "<p>已加入下载任务，详情请在上方下载列表查看详情</p><br /><span>点击前往查看</span>",
      dangerouslyUseHTMLString: true,
      duration: 0,
      onClick: () => {
        EventBus.emit("triggerDownloadTask");
        notify.close();
      },
    });
  } else {
    ElNotification.warning(res.Content);
  }
  exportLoading.value = false;
};
const handleGetQueryParams = () => {
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  Object.keys(copyData).forEach((key) => {
    if (copyData[key] === null) {
      delete copyData[key];
    }
  });
  return copyData;
};
const handleInitQueryParams = (query: any) => {
  // 使用基于规则的数组来处理查询参数，以降低圈复杂度并提高可读性。
  // 每个对象代表一个操作，包含一个触发条件和一个执行函数。
  // 顺序很重要，特别是对于可能覆盖其他参数的userId。
  const paramActions = [
    {
      // `BeginTime` 和 `EndTime` 共同定义 `DateRange`。
      // 这个操作应该在 `userId` 之前处理，因为 `userId` 可能会覆盖 `DateRange`。
      condition: () => query.BeginTime && query.EndTime,
      action: () => {
        queryParams.value.DateRange = [query.BeginTime, query.EndTime];
      },
    },
    {
      condition: () => query.AssistantId,
      action: () => {
        queryParams.value.AssistantId = query.AssistantId;
      },
    },
    {
      condition: () => query.OrgIds,
      action: () => {
        queryParams.value.OrgIds =
          typeof query.OrgIds === "string" ? query.OrgIds.split(",") : query.OrgIds;
        hospitalSelectRef.value?.handleFetchOptions();
      },
    },
    {
      condition: () => query.TimeType,
      action: () => {
        queryParams.value.TimeType = Number(query.TimeType);
      },
    },
    {
      condition: () => query.SelfReliance,
      action: () => {
        queryParams.value.SelfReliance = JSON.parse(query.SelfReliance);
      },
    },
    {
      // 如果存在任何查询参数，则设置一个默认的起始金额。
      condition: () => Object.keys(query).length > 0,
      action: () => {
        queryParams.value.StartAmount = 0.01;
      },
    },
    {
      // `userId` 会设置 `CreatorIds` 并覆盖 `DateRange`。
      // 为了确保其设置的 `DateRange` 生效，这个操作应该放在后面处理。
      condition: () => query.userId,
      action: () => {
        queryParams.value.CreatorIds = [query.userId];
        nextTick(() => {
          userSelectRef.value?.handleGetUserInfoByUserId(query.userId as string);
        });
        // 查询今天到前30天的数据
        queryParams.value.DateRange = [
          dayjs().subtract(29, "day").format("YYYY-MM-DD"),
          dayjs().format("YYYY-MM-DD"),
        ];
      },
    },
  ];

  paramActions.forEach(({ condition, action }) => {
    if (condition()) {
      action();
    }
  });
};

onActivated(() => {
  // 获取浏览器链接后面的参数
  const route = useRoute();
  // 直接获取 params
  handleInitQueryParams(route.query);
  handleGetTableList();
});
</script>

<style scoped lang="scss"></style>
