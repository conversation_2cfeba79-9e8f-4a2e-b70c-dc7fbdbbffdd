<template>
  <div class="container">
    <div class="flex items-center justify-start">
      <span>总次数</span>
      <span class="text-red-500">*</span>
      <el-input-number
        v-model="formData.TotalCount"
        :disabled="isOnlyPreview"
        :min="1"
        :max="99"
        class="m-x-10px"
      />
      <span>(范围为：(1-99))</span>
    </div>
    <div class="mt-10px m-x-auto">
      <el-transfer
        v-model="selectedScaleIds"
        :disabled="isOnlyPreview"
        filterable
        :filter-method="filterMethod"
        filter-placeholder="请输入名称"
        :data="scaleList"
        :props="{
          key: 'ScaleId',
          label: 'Name',
        }"
        :titles="['待选', '已选']"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { MoItemGroupItem, PackScaleItem } from "@/api/content/types";
import Training_Api from "@/api/training";
import { TransferDataItem } from "element-plus";
interface PageBaseGauge extends BaseGauge {
  ScaleRemarks?: string;
  ScaleId?: string;
}
const isOnlyPreview = inject("isOnlyPreview") as Ref<boolean>;
const scaleList = ref<PageBaseGauge[]>([]);
const selectedScaleIds = ref<string[]>([]);
const formData = reactive<MoItemGroupItem>({
  MoDay: 1,
  FreqDay: 1,
  Freq: 1,
  TotalCount: 1,
  Consumables: [],
  PackScales: [],
  MoItemId: "",
  MoName: "",
  MoRemark: "",
  Price: 0,
  IsSpecialFreq: false,
  MoItemMethod: 0,
  MoItemUseScope: 0,
  LogisticsDay: 0,
});

const filterMethod = (query: string, item: TransferDataItem) => {
  return item.Name.toLowerCase().includes(query.toLowerCase());
};
const handleFetchScaleListData = async () => {
  try {
    const res = await Training_Api.getEvaluateGaugePage({
      IsEnble: true,
      Keyword: "",
      PageIndex: 1,
      PageSize: 9999,
      Type: 0,
    });
    if (res.Type === 200) {
      const newData = res.Data.Rows.map((e) => {
        return {
          ...e,
          ScaleId: e.Id,
          ScaleRemarks: e.Remark,
        };
      });
      scaleList.value = newData;
    } else {
      scaleList.value = [];
    }
  } catch (error) {
    scaleList.value = [];
  }
};
const handleProcessingData = (): MoItemGroupItem => {
  // 找出selectedScaleIds对应的scaleList
  const scales = scaleList.value.filter((e) => selectedScaleIds.value.includes(e.Id!));
  const packScales: PackScaleItem[] = [];
  scales.forEach((e) => {
    const scaleItem: PackScaleItem = {
      ScaleId: e.ScaleId!,
      Name: e.Name!,
      ScaleRemarks: e.ScaleRemarks!,
    };
    packScales.push(scaleItem);
  });
  const finishData: MoItemGroupItem = {
    MoItemId: formData.MoItemId,
    MoName: formData.MoName,
    MoRemark: formData.MoRemark,
    MoDay: formData.MoDay || 1,
    Part: formData.Part || 1,
    Freq: formData.Freq || 1,
    TotalCount: formData.TotalCount,
    Price: formData.Price,
    FreqDay: formData.FreqDay || 1,
    IsSpecialFreq: formData.IsSpecialFreq,
    MoItemMethod: formData.MoItemMethod,
    MoItemUseScope: formData.MoItemUseScope,
    LogisticsDay: formData.LogisticsDay,
    PackActions: [],
    PackAcupoints: [],
    PackScales: packScales,
    ChargeMode: formData.ChargeMode,
    ChargeItem: formData.ChargeItem,
  };
  return finishData;
};
const handleSubmit = (): MoItemGroupItem | null => {
  if (!selectedScaleIds.value.length) {
    ElMessage.warning("请选择康复评定");
    return null;
  }
  const data = handleProcessingData();
  return data;
};
const handleInitPageData = () => {
  if (props.info?.PackScales && props.info.PackScales.length) {
    selectedScaleIds.value = props.info.PackScales.map((s) => s.ScaleId);
  }
};

interface Props {
  moItemId: string;
  info: MoItemGroupItem | null;
}
const props = defineProps<Props>();
watch(
  () => props.info,
  (newVal) => {
    if (newVal) {
      // 使用 Object.assign 更新属性，保持响应式
      Object.assign(formData, newVal);
      handleInitPageData();
    }
  },
  {
    immediate: true,
  }
);
defineExpose({
  handleSubmit,
});
onMounted(() => {
  handleFetchScaleListData();
});
</script>
<style scoped lang="scss">
.container {
  height: 400px;
  overflow-y: auto;
  width: 100%;
  padding: 20px;
}
:deep(.el-transfer-panel) {
  width: calc(50% - 100px);
}
</style>
