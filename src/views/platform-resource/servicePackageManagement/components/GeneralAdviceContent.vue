<template>
  <div class="container">
    <div class="flex items-center justify-start flex-wrap container-top">
      <div class="flex items-center justify-start m-x-10px">
        <span>医嘱天数</span>
        <span class="text-red-500 mr-10px">*</span>
        <el-input-number
          v-model="formData.MoDay"
          :disabled="isOnlyPreview"
          :step="1"
          step-strictly
        />
      </div>
      <div class="flex items-center justify-start m-x-10px">
        <span>频次</span>
        <span class="text-red-500 mr-10px">*</span>
        <span>一天</span>
        <el-input-number
          v-model="formData.Freq"
          :disabled="isOnlyPreview"
          :min="1"
          :max="3"
          :step="1"
          step-strictly
        />
        <span>次</span>
      </div>
      <div class="flex items-center justify-start m-x-10px">
        <span>总次数</span>
        <span class="text-red-500 mr-10px">*</span>
        <el-input-number v-model="formData.TotalCount" :step="1" step-strictly disabled />
      </div>
      <div class="flex items-center justify-start mt-10px">
        <span>病种</span>
        <el-select
          v-model="actionQuery.DiseaseDict"
          :disabled="isOnlyPreview"
          placeholder="请选择病种"
          style="width: 180px; margin: 0 10px"
          clearable
          filterable
        >
          <el-option
            v-for="item in diseaseList"
            :key="item.Id"
            :label="item.Key"
            :value="item.Id!"
          />
        </el-select>
      </div>
      <div class="flex items-center justify-start mt-10px">
        <span>训练动作</span>
        <el-select
          v-model="selectedActionIds"
          :disabled="isOnlyPreview"
          placeholder="请选择训练动作"
          style="width: 500px; margin: 0 10px"
          clearable
          multiple
          filterable
          remote
          collapse-tags
          collapse-tags-tooltip
          :max-collapse-tags="3"
          reserve-keyword
          remote-show-suffix
          :remote-method="handleFetchAction"
          :loading="actionFetchLoading"
        >
          <el-option
            v-for="item in actionList"
            :key="item.ContentId"
            :label="item.Name"
            :value="item.ContentId"
          />
        </el-select>
      </div>
      <el-button
        v-if="!groupByList.length"
        type="primary"
        class="mt-10px"
        :disabled="isOnlyPreview"
        @click="handleAddAction"
      >
        添加
      </el-button>
    </div>
    <div ref="containerTableRef" class="container-table">
      <el-card v-for="(item, index) in groupByList" :key="index" style="margin-top: 12px">
        <div slot="header" ref="actionRefs" class="clearfix">
          <el-input
            v-model="item.Type"
            style="width: 450px"
            :disabled="isOnlyPreview"
            placeholder="请输入内容"
            clearable
          />
          <el-button
            style="float: right; padding: 3px 0"
            :disabled="isOnlyPreview"
            link
            type="primary"
            @click="handleAddPhase(index)"
          >
            添加阶段
          </el-button>
          <el-button
            style="float: right; padding: 3px 0; margin-right: 10px; color: red"
            link
            :disabled="isOnlyPreview"
            @click="handleDeletePhase(index)"
          >
            删除阶段
          </el-button>
          <el-button
            style="float: right; padding: 3px 0; margin-right: 10px"
            link
            type="primary"
            :disabled="isOnlyPreview"
            @click="handleAddActionByIndex(index)"
          >
            添加动作
          </el-button>
        </div>
        <div class="mt-10px">
          <el-table
            :id="`table-${index}`"
            ref="tableRef"
            class="w-full"
            :data="item.Data"
            highlight-current-row
            border
            :row-key="(row) => row.ContentId"
            stripe
            align="center"
          >
            <el-table-column
              prop="Name"
              label="训练动作"
              :show-overflow-tooltip="true"
              align="center"
            />
            <el-table-column label="频次" :show-overflow-tooltip="true" align="center">
              <template #default="{ row }">1天 {{ row.Freq }}次</template>
            </el-table-column>
            <el-table-column
              prop="ActionTime"
              label="运动时间"
              :show-overflow-tooltip="true"
              align="center"
            />
            <el-table-column
              prop="ActionStrength"
              label="运动强度"
              :show-overflow-tooltip="true"
              align="center"
            />
            <el-table-column
              prop="Notes"
              label="注意事项"
              :show-overflow-tooltip="true"
              align="center"
            />
            <el-table-column
              prop="InstrumentParameter"
              label="参数"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template #default="{ row }">
                <span>{{ handleGetInstrumentParameter(row.InstrumentParameter) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="{ row }">
                <el-button link type="primary" @click="handleEditAction(row, index)">
                  {{ isOnlyPreview ? "查看" : "修改" }}
                </el-button>
                <el-button
                  link
                  type="primary"
                  :disabled="isOnlyPreview"
                  @click="handleDeleteAction(row, index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
    <el-dialog
      v-model="showDialog.generalAction"
      :title="updateActionTitle"
      width="50%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <GeneralAction ref="generalActionRef" :update-action-data="updateActionData" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog.generalAction = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleSubmitGeneralAction"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import Sortable from "sortablejs";
import Content_Api from "@/api/content";
import { MoItemAction, MoItemActionsInputDTO, MoItemGroupItem } from "@/api/content/types";
import { listGroupByKey } from "@/utils";
import { ElTable } from "element-plus";
import GeneralAction from "./GeneralAction.vue";

// 1. 定义新的辅助函数 getOrderedActions
function getOrderedActions(
  selectedIds: string[],
  allActions: Readonly<MoItemAction[]> // 使用 Readonly 避免意外修改原始 actionList
): MoItemAction[] {
  return selectedIds
    .map((id) => {
      const action = allActions.find((a) => a.ContentId === id);
      return action ? { ...action, Freq: formData.Freq } : null; // 浅拷贝
    })
    .filter((action): action is MoItemAction => action !== null);
}

const diseaseList = inject("diseaseList") as Ref<ReadDict[]>;
const isOnlyPreview = inject("isOnlyPreview") as Ref<boolean>;
const containerTableRef = ref<HTMLElement>();
const actionFetchLoading = ref(false);
const actionList = ref<MoItemAction[]>([]);
const selectedActionIds = ref<string[]>([]);
const sortableInstances = ref<Sortable[]>([]);
const tableRef = ref<InstanceType<typeof ElTable>[]>([]);
const groupByList = ref<GroupedItem<MoItemAction>[]>([]);
const updateOutIndex = ref<number>(-1);
const actionQuery = ref<MoItemActionsInputDTO>({
  MoItemId: "",
  PackId: null,
  PageIndex: 1,
  PageSize: 20,
  DiseaseDict: "",
  DtoName: null,
  Keyword: "",
});
const showDialog = reactive({
  generalAction: false,
});
const updateActionTitle = ref<string>("");
const dialogConfirmLoading = ref<boolean>(false);
const updateActionData = ref<MoItemAction | null>(null);
const formData = reactive<MoItemGroupItem>({
  MoDay: 1,
  FreqDay: 1,
  Freq: 1,
  TotalCount: 1,
  MoItemId: "",
  MoName: "",
  MoRemark: "",
  Price: 0,
  IsSpecialFreq: false,
  MoItemMethod: 0,
  MoItemUseScope: 0,
  LogisticsDay: 0,
  PackActions: [],
});
const generalActionRef = ref<InstanceType<typeof GeneralAction> | null>(null);

watch(
  () => [formData.MoDay, formData.Freq],
  () => {
    formData.TotalCount = formData.MoDay * formData.Freq;
  }
);

const handleAddAction = () => {
  if (!selectedActionIds.value.length) {
    ElMessage.warning("请选择训练动作");
    return;
  }
  // 2. 修改 handleAddAction 以使用 getOrderedActions
  const orderedSelectedActions = getOrderedActions(selectedActionIds.value, actionList.value);

  if (!orderedSelectedActions.length) {
    // 如果没有找到任何有效的动作（理论上不应发生，因为selectedActionIds来自actionList）
    return;
  }

  // 将 Manufacturer 应用到有序列表中的每个动作
  orderedSelectedActions.forEach((action) => {
    if (formData.Manufacturer === 1) {
      action.Manufacturer = formData.Manufacturer;
    }
  });

  let groupType: string = formData.MoName; // 默认分组类型
  if (actionQuery.value.DiseaseDict) {
    // 获取到疾病的名称
    const diseaseName = diseaseList.value.find((s) => s.Id === actionQuery.value.DiseaseDict)?.Key;
    if (diseaseName) {
      groupType = diseaseName;
    }
  }

  groupByList.value = [
    {
      Type: groupType,
      Data: orderedSelectedActions, // 使用有序的动作列表
    },
  ];

  selectedActionIds.value = [];
  nextTick(() => {
    onSortableRow();
  });
};

const handleAddActionByIndex = (index: number) => {
  if (!selectedActionIds.value.length) {
    ElMessage.warning("请选择训练动作");
    return;
  }
  // 3. 修改 handleAddActionByIndex 以使用 getOrderedActions
  const orderedActionsToAdd = getOrderedActions(selectedActionIds.value, actionList.value);

  if (!orderedActionsToAdd.length) {
    // 如果没有找到任何有效的动作
    return;
  }

  // 获取当前分组
  const currentGroup = groupByList.value[index];
  if (!currentGroup) {
    // 防御性检查，确保分组存在
    ElMessage.error("指定的分组不存在");
    return;
  }

  if (!currentGroup.Type) {
    if (actionQuery.value.DiseaseDict) {
      const diseaseName = diseaseList.value.find(
        (s) => s.Id === actionQuery.value.DiseaseDict
      )?.Key;
      // 确保 diseaseName 存在，并为 Type 赋一个唯一值，以防多个未命名阶段因病种而同名
      currentGroup.Type = diseaseName
        ? `${diseaseName}-${groupByList.value.length}`
        : `阶段-${groupByList.value.length}`;
    } else {
      currentGroup.Type = formData.MoName
        ? `${formData.MoName}-${groupByList.value.length}`
        : `阶段-${groupByList.value.length}`;
    }
  }

  // 为即将添加的动作设置制造商信息（如果需要）
  // 这些动作已经是浅拷贝，可以直接修改
  orderedActionsToAdd.forEach((action) => {
    if (formData.Manufacturer === 1) {
      action.Manufacturer = formData.Manufacturer;
    }
  });

  // 过滤掉已存在的动作
  const newActions = orderedActionsToAdd.filter(
    (actionToAdd) =>
      !currentGroup.Data.some(
        (existingAction) => existingAction.ContentId === actionToAdd.ContentId
      )
  );

  if (newActions.length === 0) {
    ElMessage.warning("所选动作已全部存在于当前分组中");
    // 即使没有新动作添加，也应该清空 selectedActionIds，因为用户意图是"添加这些选中的动作"
    selectedActionIds.value = [];
    return;
  }

  // 添加新动作到分组
  currentGroup.Data = [...currentGroup.Data, ...newActions];
  // 清空选择
  selectedActionIds.value = [];
};

const handleFetchAction = async (query: string) => {
  actionQuery.value.MoItemId = formData.MoItemId;
  actionFetchLoading.value = true;
  actionQuery.value.Keyword = query;
  if (query) {
    actionQuery.value.PageSize = 9999;
  } else {
    actionQuery.value.PageSize = 100;
  }
  const res = await Content_Api.getMoItemActions(actionQuery.value);
  if (res.Type === 200) {
    res.Data.forEach((s) => {
      if (s.Media) delete s.Media;
      if (s.Instrument) s.InstrumentId = s.Instrument;
      delete s.Instrument;
    });
    actionList.value = res.Data;
  }
  actionFetchLoading.value = false;
};
const handleInitPageData = () => {
  if (formData.PackActions && formData.PackActions.length > 0) {
    const groupByListData = listGroupByKey(formData.PackActions!, "Group");
    groupByListData.forEach((s) => {
      s.Data.forEach((s) => {
        s.Group = s.Group || "第1阶段";
      });
    });
    groupByList.value = groupByListData;
    nextTick(() => {
      onSortableRow();
    });
  }
};
const handleDeleteAction = (row: MoItemAction, index: number) => {
  ElMessageBox.confirm("确认删除该训练动作吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      const targetGroup = groupByList.value[index];
      targetGroup.Data = targetGroup.Data.filter((item) => item.ContentId !== row.ContentId);
      // 如果该分组没有数据了,则删除整个分组
      if (targetGroup.Data.length === 0) {
        groupByList.value.splice(index, 1);
      }
      ElMessage.success("删除成功");
    })
    .catch(() => {
      // 取消删除操作
    });
};
const handleEditAction = (row: MoItemAction, index: number) => {
  updateOutIndex.value = index;
  updateActionTitle.value = `修改${row.Name}`;
  updateActionData.value = row;
  showDialog.generalAction = true;
};
const handleSubmitGeneralAction = () => {
  const info = generalActionRef.value?.handleSubmit();
  if (info) {
    // 找到当前分组
    const currentGroup = groupByList.value[updateOutIndex.value];
    if (currentGroup) {
      // 在分组内根据 ContentId 更新对应的动作数据
      const index = currentGroup.Data.findIndex((item) => item.ContentId === info.ContentId);
      if (index !== -1) {
        currentGroup.Data[index] = info;
      }
    }
    // 关闭弹窗并重置状态
    showDialog.generalAction = false;
    updateActionData.value = null;
    updateOutIndex.value = -1;
  }
};
const handleGetInstrumentParameter = (instrumentParameter: string) => {
  if (!instrumentParameter) return "";
  try {
    const data = JSON.parse(instrumentParameter);
    return (
      data &&
      data
        .map((item: BaseInstrumentParameter) => {
          return `${item.Name}: ${item.ValueLabel}`;
        })
        .join(";")
    );
  } catch (error) {
    console.error("解析仪器参数失败:", error);
    return "";
  }
};
const handleDeletePhase = (index: number) => {
  ElMessageBox.confirm("确认删除该阶段吗？删除后该阶段下的所有动作都将被删除", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      groupByList.value.splice(index, 1);
      ElMessage.success("删除成功");
    })
    .catch(() => {
      // 取消删除操作
    });
};

const handleAddPhase = (index: number) => {
  // 在当前阶段后插入新阶段
  const newPhase = {
    Type: ``,
    Data: [],
  };
  groupByList.value.splice(index + 1, 0, newPhase);

  // 等待DOM更新后滚动到新添加的阶段
  nextTick(() => {
    const cards = containerTableRef.value?.getElementsByClassName("el-card");
    if (cards && cards[index + 1]) {
      cards[index + 1].scrollIntoView({ behavior: "smooth", block: "center" });
    }
  });
  if (selectedActionIds.value.length) {
    handleAddActionByIndex(index + 1);
  }

  ElMessage.success("添加成功");
};

// 拖动表格行
const onSortableRow = () => {
  nextTick(() => {
    sortableInstances.value.forEach((instance) => {
      if (instance) instance.destroy();
    });
    sortableInstances.value = [];

    groupByList.value.forEach((_, groupIndex) => {
      const table = document.querySelector(
        `#table-${groupIndex} .el-table__body-wrapper tbody`
      ) as HTMLElement;
      if (table) {
        const sortable = Sortable.create(table, {
          animation: 150,
          onEnd: ({ newIndex, oldIndex }) => {
            if (typeof newIndex === "number" && typeof oldIndex === "number") {
              const currentGroup = groupByList.value[groupIndex];
              const movedItem = currentGroup.Data[oldIndex];
              currentGroup.Data.splice(oldIndex, 1);
              currentGroup.Data.splice(newIndex, 0, movedItem);
            }
          },
        });
        sortableInstances.value[groupIndex] = sortable;
      }
    });
  });
};

// 添加watch以监听数据变化
watch(
  () => groupByList.value,
  () => {
    nextTick(() => {
      onSortableRow();
    });
  },
  { deep: true }
);

// 修改生命周期钩子
onMounted(() => {
  nextTick(() => {
    onSortableRow();
  });
});

onBeforeUnmount(() => {
  sortableInstances.value.forEach((instance) => {
    if (instance) instance.destroy();
  });
  sortableInstances.value = [];
});

const getParameterError = (action: MoItemAction): string => {
  return `动作"${action.Name}"的仪器参数未设置,请修改后重试`;
};

const validateActionParameters = (action: MoItemAction): string | null => {
  if (!action.IsIOT && action.Manufacturer !== 2) return null;
  if (!action.InstrumentParameter) return getParameterError(action);

  try {
    const parameters = JSON.parse(action.InstrumentParameter);
    const invalidParam = parameters.find(
      (param: BaseInstrumentParameter) =>
        param.Value === undefined || param.Value === null || param.Value === ""
    );
    return invalidParam
      ? `动作"${action.Name}"的参数"${invalidParam.Name}"未设置,请修改后重试`
      : null;
  } catch {
    return `动作"${action.Name}"的仪器参数格式错误`;
  }
};

const validateAllActions = (groups: GroupedItem<MoItemAction>[]): string | null => {
  for (const group of groups) {
    for (const action of group.Data) {
      const error = validateActionParameters(action);
      if (error) return error;
    }
  }
  return null;
};

const preparePackActions = (groups: GroupedItem<MoItemAction>[]): MoItemAction[] => {
  return groups
    .map((group) =>
      group.Data.map((action) => ({
        ...action,
        Group: group.Type,
      }))
    )
    .flat();
};

const buildSubmitData = (): MoItemGroupItem => {
  return {
    MoItemId: formData.MoItemId,
    MoName: formData.MoName,
    MoDay: formData.MoDay,
    MoMonth: formData.MoMonth,
    MoRemark: formData.MoRemark,
    Freq: formData.Freq,
    Part: formData.Part || 1,
    TotalCount: formData.TotalCount,
    FreqDay: formData.FreqDay || 1,
    IsSpecialFreq: formData.IsSpecialFreq,
    LogisticsDay: formData.LogisticsDay,
    MoItemMethod: formData.MoItemMethod,
    MoItemUseScope: formData.MoItemUseScope,
    PackActions: preparePackActions(groupByList.value),
    PackAcupoints: [],
    PackScales: [],
    Manufacturer: formData.Manufacturer,
    Price: formData.Price || 0,
    ChargeMode: formData.ChargeMode,
    ChargeItem: formData.ChargeItem,
  };
};

const handleSubmit = (): MoItemGroupItem | null => {
  if (formData.Freq > 3 || formData.Freq < 1) {
    ElMessage.warning("频次请填写1-3之间的整数");
    return null;
  }

  if (formData.MoDay > formData.MaxDay! || formData.MoDay < formData.MinDay!) {
    ElMessage.warning(`医嘱天数请设置在${formData.MinDay!}到${formData.MaxDay!}`);
    return null;
  }

  if (groupByList.value.length) {
    if (groupByList.value.some((s) => !s.Type)) {
      ElMessage.warning("请填写阶段名称");
      return null;
    }

    const validationError = validateAllActions(groupByList.value);
    if (validationError) {
      ElMessage.warning(validationError);
      return null;
    }
  }

  return buildSubmitData();
};

interface Props {
  moItemId: string;
  info: MoItemGroupItem | null;
}
const props = defineProps<Props>();
watch(
  () => props.info,
  (newVal) => {
    if (newVal) {
      Object.assign(formData, newVal);
      handleInitPageData();
    }
  },
  {
    immediate: true,
  }
);
defineExpose({
  handleSubmit,
});
</script>
<style scoped lang="scss">
.container {
  height: 600px;
  width: 100%;
  padding: 20px 20px 0 20px;
  position: relative;

  &-top {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 10;
    padding: 10px 0;
    width: 100%;
    border-bottom: 1px solid #ebeef5;
  }

  &-table {
    margin-top: 10px;
    overflow-y: auto;
    height: calc(100% - 140px);
  }

  &-card {
    position: relative;
  }
}
</style>
