<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto">
    <el-form :ref="kFormRef" :model="formData" :rules="rules" label-width="80px">
      <el-form-item label="名称" prop="Name">
        <el-input v-model="formData.Name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="编码" prop="Code">
        <el-input v-model="formData.Code" placeholder="请输入编码" />
      </el-form-item>
      <el-form-item label="上级分类" prop="ParentId">
        <el-select ref="selectRef" v-model="formData.ParentName" placeholder="请选择">
          <template #empty>
            <el-tree
              class="w-full h-full p-10px"
              :data="props.recoveryTypes"
              :props="{ label: 'Name', children: 'Children' }"
              node-key="Id"
              :current-node-key="formData.ParentId"
              highlight-current
              default-expand-all
              @node-click="onTreeClick"
            />
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="是否启用" prop="Enable">
        <el-switch v-model="formData.Enable" />
      </el-form-item>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormRules, FormInstance } from "element-plus";
import {
  CreateRecoveryMissionTypeParams,
  UpdateRecoveryMissionTypeParams,
} from "@/api/content/types";
import Content_Api from "@/api/content";

interface FormData extends CreateRecoveryMissionTypeParams {
  ParentName?: string;
}

const kEnableDebug = false;
const kFormRef = "ruleFormRef";

defineOptions({
  name: "RecoveryMissionTypeForm",
});

const props = defineProps<{
  /** 父级分类 */
  parent?: RecoveryMissionType;
  /** 当前分类 */
  data?: RecoveryMissionType;
  /** 康复分类 */
  recoveryTypes: RecoveryMissionType[];
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

const formLoading = ref(false);
// 表单实例
const formRef = useTemplateRef<FormInstance>(kFormRef);
// 表单数据
const formData = reactive<FormData>({
  Name: props.data?.Name ?? "",
  Code: props.data?.Code ?? "",
  Enable: props.data?.Enable ?? true,
  OrganizationId: props.data?.OrganizationId,
  ParentId: props.parent?.Id,
  ParentName: props.parent?.Name,
});

// 表单验证规则
const rules = reactive<FormRules<FormData>>({
  Name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  Code: [{ required: true, message: "请输入编码", trigger: "blur" }],
  ParentId: [{ required: true, message: "请选择上级分类", trigger: "change" }],
});

// 提交表单
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid && !props.data?.Id) {
      requestAddData();
    } else if (valid && props.data?.Id) {
      requestUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields);
    }
  });
}

/** 选择上级分类 */
const selectRef = useTemplateRef("selectRef");

// 点击选择类别
function onTreeClick(data: RecoveryMissionType) {
  kEnableDebug && console.debug("点击选择上级分类", data);

  formData.ParentId = data.Id;
  formData.ParentName = data.Name;

  // 主动关闭类别弹窗
  selectRef.value?.blur();
}

/** 添加数据 */
async function requestAddData() {
  formLoading.value = true;
  const params: CreateRecoveryMissionTypeParams = {
    Name: formData.Name,
    Code: formData.Code,
    Enable: formData.Enable,
    ParentId: formData.ParentId,
    OrganizationId: formData.OrganizationId,
  };
  const r = await Content_Api.createRecoveryMissionType(params);
  formLoading.value = false;
  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}

/** 更新数据 */
async function requestUpdateData() {
  if (!props.parent?.Enable && formData.Enable) {
    // 启用当前节点，需要父级启用
    ElMessageBox.confirm("请将父级改为启用状态后，再来启用该分类", "提示", {
      confirmButtonText: "确定",
      showCancelButton: false,
      type: "warning",
    });
    formData.Enable = false;
    return;
  }

  if (!formData.Enable) {
    // 关闭当前节点，需要检查是否有启动的子节点
    formLoading.value = true;
    const r0 = await Content_Api.recoveryMissionTypeHaveEnables(props.data!.Id!);
    if (r0.Type !== 200) {
      formLoading.value = false;
      ElMessageBox.confirm(r0.Content, "提示", {
        confirmButtonText: "确定",
        showCancelButton: false,
        type: "warning",
      });
      formData.Enable = true;
      return;
    } else if (r0.Type === 200 && r0.Data) {
      formLoading.value = false;
      ElMessageBox.confirm("此分类有子分类或宣教内容数据不可禁用", "提示", {
        confirmButtonText: "确定",
        showCancelButton: false,
        type: "warning",
      });
      formData.Enable = true;
      return;
    }
  }

  // 更新数据
  formLoading.value = true;
  const params: UpdateRecoveryMissionTypeParams = {
    Id: props.data!.Id!,
    Name: formData.Name,
    Code: formData.Code,
    Enable: formData.Enable,
    ParentId: formData.ParentId,
    OrganizationId: formData.OrganizationId,
  };
  const r1 = await Content_Api.updateRecoveryMissionType(params);
  formLoading.value = false;
  if (r1.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r1.Content);
  }
}
</script>

<style lang="scss" scoped></style>
