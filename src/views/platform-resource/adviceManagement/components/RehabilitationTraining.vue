<template>
  <div class="flex items-center justify-between h-full">
    <div :style="{ width: 'calc(60% - 100px)' }">
      <!-- 搜索 -->
      <div class="flex items-center justify-between mb-10px">
        <el-input v-model="queryParams.Keyword" clearable placeholder="请输入名称" />
        <el-button type="primary" class="m-x-20px" @click="handleGetActionUnitList">搜索</el-button>
      </div>
      <el-table
        ref="leftTableRef"
        v-loading="sourceTableLoading"
        :data="sourceTableData"
        highlight-current-row
        row-key="ContentId"
        border
        :disabled="isPreview"
        :height="tableHeight"
        style="text-align: center; width: 100%"
        @select="handleLeftTableSelect"
        @select-all="handleLeftTableSelect"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
          :disabled="isPreview"
          reserve-selection
        />
        <el-table-column prop="Name" label="名称" align="center" />
        <el-table-column prop="Code" label="编码" align="center" />
      </el-table>
      <Pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.PageIndex"
        v-model:limit="queryParams.PageSize"
        layout="prev, pager, next"
        size="small"
        @pagination="handleGetActionUnitList"
      />
    </div>
    <div :style="{ width: 'calc(60% - 100px)' }">
      <div class="h-50px" />
      <el-table
        ref="rightTableRef"
        :data="selectedTableData"
        highlight-current-row
        row-key="ContentId"
        border
        :height="tableHeight"
        style="text-align: center; width: 100%"
        width="200"
      >
        <el-table-column prop="Name" label="是否默认" align="center" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.IsDefault"
              :disabled="isPreview"
              @change="(event) => handleSwitchChange(row, event as boolean)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="Name" label="名称" align="center" />
        <el-table-column label="操作" align="center">
          <template #default="{ row }">
            <el-button type="primary" :disabled="isPreview" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="h-56px" />
    </div>
  </div>
</template>

<script setup lang="ts">
import Content_Api from "@/api/content";
import { PageQueryActionUnitParams } from "@/api/content/types";
import { ElTable } from "element-plus";
export interface PageRightTableData {
  IsDefault?: boolean;
  ContentId: string;
  Name: string;
}
const leftTableRef = ref<InstanceType<typeof ElTable> | null>(null);
const sourceTableData = ref<ActionUnit[]>([]);
const cacheSourceTableData = ref<{ [key: string]: ActionUnit }>({});
const total = ref<number>(0);
const sourceTableLoading = ref<boolean>(false);
const tableHeight = ref<number>(350);
const selectedTableData = ref<PageRightTableData[]>([]);
const queryParams = ref<PageQueryActionUnitParams>({
  PageIndex: 1,
  PageSize: 20,
  Enable: true,
  Keyword: "",
  UseScope: 0,
  Type: 0,
});
const isPreview = inject("isPreview") as Ref<boolean>;

const handleGetActionUnitList = async () => {
  const res = await Content_Api.pageQueryActionUnit(queryParams.value);
  if (res.Type === 200 && res.Data.TotalCount) {
    total.value = res.Data.TotalCount;
    res.Data.Data.forEach((item) => {
      if (!cacheSourceTableData.value[item.ContentId]) {
        cacheSourceTableData.value[item.ContentId] = item;
      }
    });
    let list: ActionUnit[] = [];
    Object.values(cacheSourceTableData.value).forEach((item) => {
      res.Data.Data.forEach((s) => {
        if (s.ContentId === item.ContentId) {
          list.push(item);
        }
      });
    });
    sourceTableData.value = list;
    // 处理已选中的数据
    handleProcessIsSelectedData();
  }
};
const handleProcessIsSelectedData = () => {
  Object.values(cacheSourceTableData.value).forEach((item) => {
    const isPresence = selectedTableData.value.some((s) => s.ContentId === item.ContentId);
    if (isPresence) {
      nextTick(() => {
        leftTableRef.value?.toggleRowSelection(item, true);
      });
    }
  });
};
const handleLeftTableSelect = (selection: ActionUnit[], row: ActionUnit) => {
  // 说明是单选
  if (row) {
    if (!selectedTableData.value.some((s) => s.ContentId === row.ContentId)) {
      selectedTableData.value.push({
        ContentId: row.ContentId,
        Name: row.Name,
      });
    } else {
      selectedTableData.value = selectedTableData.value.filter(
        (s) => s.ContentId !== row.ContentId
      );
    }
  } else {
    // 说明是全选
    if (selection.length) {
      selection.forEach((s) => {
        if (!selectedTableData.value.some((v) => s.ContentId === v.ContentId)) {
          selectedTableData.value.push({
            ContentId: s.ContentId,
            Name: s.Name,
          });
        }
      });
    } else {
      sourceTableData.value.forEach((s) => {
        selectedTableData.value.forEach((v) => {
          if (s.ContentId === v.ContentId) {
            selectedTableData.value = selectedTableData.value.filter(
              (k) => k.ContentId !== s.ContentId
            );
          }
        });
      });
    }
  }
};
const handleSwitchChange = (row: PageRightTableData, event: boolean) => {
  // 如果为true，则将其他行的IsDefault设置为false
  if (event) {
    selectedTableData.value.forEach((item) => {
      if (item.ContentId !== row.ContentId) {
        item.IsDefault = false;
      }
    });
  }
  row.IsDefault = event;
};
const handleSubmitData = (): { Id: string; IsDefault: boolean }[] => {
  return (
    selectedTableData.value?.map((s) => ({
      Id: s.ContentId as string,
      IsDefault: s.IsDefault || false,
    })) || []
  );
};
const handleDelete = (row: PageRightTableData) => {
  const findItem = Object.values(cacheSourceTableData.value).find(
    (item) => item.ContentId === row.ContentId
  );
  if (findItem) {
    nextTick(() => {
      leftTableRef.value?.toggleRowSelection(findItem, false);
    });
  }
  selectedTableData.value = selectedTableData.value.filter(
    (item) => item.ContentId !== row.ContentId
  );
};
const handleGetSelectedTableData = (data: PageRightTableData[]) => {
  selectedTableData.value = data || [];
};
interface Props {
  trainingList: PageRightTableData[] | null;
}
const props = defineProps<Props>();
watch(
  () => props.trainingList,
  (newVal) => {
    if (newVal) {
      handleGetSelectedTableData(newVal);
    }
  },
  {
    immediate: true,
  }
);
defineExpose({
  handleSubmitData,
});
onMounted(() => {
  // 获取康复训练
  handleGetActionUnitList();
});
</script>

<style lang="scss" scoped></style>
