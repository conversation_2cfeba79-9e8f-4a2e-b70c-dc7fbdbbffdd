<template>
  <div class="h-500px overflow-y-auto p-20px p-t-0px">
    <el-tabs v-model="activeName" type="card" class="advice-tabs" @tab-click="handleTabsClick">
      <el-tab-pane label="基本信息" name="Base">
        <Base
          ref="baseRef"
          :formItem="formItem"
          @getIsShowTreatmentPoint="handleIsShowTreatmentPoint"
        />
      </el-tab-pane>
      <el-tab-pane label="关联康复训练" name="RehabilitationTraining">
        <RehabilitationTraining
          ref="rehabilitationTrainingRef"
          :trainingList="trainingList"
          :organization-id="props.info?.OrganizationId || ''"
        />
      </el-tab-pane>
      <el-tab-pane label="关联费用" name="Cost">
        <Cost ref="costRef" :chargeItems="chargeItems" />
      </el-tab-pane>
      <el-tab-pane v-if="isShowTreatmentPoint" label="适用治疗点" name="TreatmentPoint">
        <TreatmentPoint ref="treatmentPointRef" :treatSelectData="treatSelectData" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import Base from "./Base.vue";
import RehabilitationTraining, { PageRightTableData } from "./RehabilitationTraining.vue";
import Cost, { CostTableProps } from "./Cost.vue";
import TreatmentPoint from "./TreatmentPoint.vue";
import { TabsPaneContext } from "element-plus";
import { AdviceMoItemMethod, AdviceMoItemUseScope } from "@/enums/AdviceEnum";
import { InsertOrUpdateMoItemInputDTO } from "@/api/content/types";
import Content_Api from "@/api/content";
export interface PageInsertOrUpdateMoItemParams
  extends Omit<InsertOrUpdateMoItemInputDTO, "Scene" | "Visibility"> {
  Scene: string[];
  Visibility: string[];
}
const isShowTreatmentPoint = ref<boolean>(false);
const trainingList = ref<PageRightTableData[]>([]);
const treatSelectData = ref<string[]>([]);
const formItem = ref<PageInsertOrUpdateMoItemParams | null>(null);
const chargeItems = ref<CostTableProps[]>([]);
const activeName = ref<string>("Base");
const baseRef = ref<InstanceType<typeof Base> | null>(null);
const rehabilitationTrainingRef = ref<InstanceType<typeof RehabilitationTraining> | null>(null);
const costRef = ref<InstanceType<typeof Cost> | null>(null);
const treatmentPointRef = ref<InstanceType<typeof TreatmentPoint> | null>(null);
const handleTabsClick = (tab: TabsPaneContext) => {
  activeName.value = tab.paneName as string;
};
const handleIsShowTreatmentPoint = (value: boolean) => {
  isShowTreatmentPoint.value = value;
};
const handleSubmitData = async (): Promise<InsertOrUpdateMoItemInputDTO | null> => {
  const baseData = await baseRef.value?.handleFormSubmit();
  const rehabilitationTrainingData = rehabilitationTrainingRef.value?.handleSubmitData();
  const treatmentPointData = treatmentPointRef.value?.handleSelectFinish();
  const costData = costRef.value?.handleCostFinish();
  if (!validateFormData(baseData, rehabilitationTrainingData, costData, treatmentPointData)) {
    return null;
  }
  return handleProcessingData(baseData, rehabilitationTrainingData, costData, treatmentPointData);
};
const processScene = (scenes: string[]) =>
  scenes.length > 1 ? scenes.map(Number).reduce((acc, num) => acc | num, 0) : Number(scenes[0]);

const processChargeItems = (copyCostData: CostTableProps[] | undefined, moItemId: string) => {
  const details =
    copyCostData?.map((item) => ({
      ChargeItemId: item.Id,
      Quantity: item.Quantity,
    })) || [];
  return [{ MoItemId: moItemId, PackChargeItemDetails: details, PackId: undefined }];
};

const processTrainingData = (copyTrainData: { Id: string; IsDefault: boolean }[]) => ({
  TrainingItems: copyTrainData.map((s: { Id: string }) => s.Id) || [],
  DefaultTrainingItem: copyTrainData.find((s: { IsDefault: boolean }) => s.IsDefault)?.Id || "",
});

const handleProcessingData = (
  baseData: PageInsertOrUpdateMoItemParams,
  trainData: { Id: string; IsDefault: boolean }[] | undefined,
  costData: CostTableProps[] | undefined,
  treatmentPointData: string[] | undefined
): InsertOrUpdateMoItemInputDTO => {
  const copyBaseData = JSON.parse(JSON.stringify(baseData));
  const copyTrainData = JSON.parse(JSON.stringify(trainData || []));
  const copyCostData = JSON.parse(JSON.stringify(costData || []));
  const copyTreatmentPointData = JSON.parse(JSON.stringify(treatmentPointData || []));

  return {
    ...copyBaseData,
    Scene: processScene(copyBaseData.Scene),
    Visibility: processScene(copyBaseData.Visibility),
    ...processTrainingData(copyTrainData),
    ChargeItems: processChargeItems(copyCostData, copyBaseData.ObjectId || ""),
    Treats: copyTreatmentPointData || [],
  };
};
const validateFormData = (
  baseData: PageInsertOrUpdateMoItemParams | undefined,
  trainData: { Id: string; IsDefault: boolean }[] | undefined,
  costData: CostTableProps[] | undefined,
  treatmentPointData: string[] | undefined
) => {
  if (!baseData) {
    ElMessage.error("请填写完整基本信息");
    return false;
  }
  if (baseData.MoItemMethod !== AdviceMoItemMethod.General) {
    if (trainData?.length) {
      ElMessage.error("下达方式为普通时，才能设置康复训练，请前往“康复训练”标签页进行删除");
      return false;
    }
  } else if (!trainData?.length) {
    ElMessage.error("请选择康复训练");
    return false;
  }
  if (baseData.MoItemUseScope === AdviceMoItemUseScope.Community && !treatmentPointData?.length) {
    ElMessage.error("请选择治疗点");
    return false;
  }
  return validateCostData(baseData, costData);
};
const validateCostData = (
  baseData: PageInsertOrUpdateMoItemParams,
  costData: CostTableProps[] | undefined
) => {
  let totalAmount = 0;
  if (costData?.length) {
    totalAmount = costData.reduce((acc, item) => acc + item.Quantity! * item.Price, 0);
  }
  if (totalAmount < Number(baseData.MinAmount) || totalAmount > Number(baseData.MaxAmount)) {
    ElMessage.error("收费项目的总价格不在价格范围内，请重新设置价格范围或者修改收费项目");
    return false;
  }
  if (baseData.ShowAmount !== null && baseData.ShowAmount < totalAmount) {
    ElMessage.error("指导价格小于收费项目的总价，请重新设置指导价或者修改收费项目");
    return false;
  }
  return true;
};
const processVisibility = (data: BaseMoItemData) => {
  const Visibility = [];
  if (data.DoctorVisibility) Visibility.push("1");
  if (data.TherapistVisibility) Visibility.push("2");
  if (data.TherapistByDoctorVisibility) Visibility.push("4");
  if (data.NurseVisibility) Visibility.push("8");
  if (data.NurseByDoctorVisibility) Visibility.push("16");
  return Visibility;
};
// 清理数据属性函数
const cleanupDataProperties = (data: BaseMoItemData) => {
  delete data.ConsultScene;
  delete data.PatientManagerScene;
  delete data.ConsumablesOutputDtos;
  delete data.DoctorVisibility;
  delete data.TherapistVisibility;
  delete data.TherapistByDoctorVisibility;
  delete data.NurseVisibility;
  delete data.NurseByDoctorVisibility;
};

// 处理场景数据转换函数
const processSceneData = (data: BaseMoItemData) => {
  const Scene = [];
  if (data.ConsultScene) Scene.push("1");
  if (data.PatientManagerScene) Scene.push("2");
  return Scene;
};

// 构建表单项数据函数
const buildFormItemData = (data: BaseMoItemData, Scene: string[], Visibility: string[]) => {
  return {
    MoItemUseScope: data.MoItemUseScope,
    Name: data.Name,
    AliasName: data.AliasName,
    PYM: data.PYM,
    Code: data.Code,
    MoType: data.MoType,
    Manufacturer: data.Manufacturer,
    MoItemMethod: data.MoItemMethod,
    ChargeMode: data.ChargeMode,
    IsUseConsumable: data.IsUseConsumable,
    IsEnable: data.IsEnable,
    IsDefaultPush: data.IsDefaultPush,
    Catelog: data.Catelog,
    Tags: data.Tags,
    MinAmount: data.MinAmount,
    MaxAmount: data.MaxAmount,
    ShowAmount: data.ShowAmount,
    DefaultMoDay: data.DefaultMoDay,
    LogisticsDay: data.LogisticsDay,
    MinDay: data.MinDay,
    MaxDay: data.MaxDay,
    Explain: data.Explain,
    Media: data.Media,
    Remark: data.Remark,
    HaveConsultServe: data.HaveConsultServe,
    IsSpecialFreq: data.IsSpecialFreq,
    MoItemVideoUrl: data.MoItemVideoUrl,
    Scene,
    Visibility,
    Consumables: data.Consumables || [],
    ObjectId: data.Id || undefined,
    OrganizationId: data.OrganizationId || "",
    DeptIds: data.DeptIds || [],
  };
};

// 处理收费项目数据函数
const processChargeItemsData = (data: BaseMoItemData) => {
  const chargeItemsInfo = data.ChargeItems || [];
  if (chargeItemsInfo.length > 0) {
    const newChargeItems = chargeItemsInfo[0].PackChargeItemDetails.map((item): CostTableProps => {
      return {
        Code: item.Code ?? "",
        Id: item.ChargeItemId ?? "",
        IsEnabled: true,
        Name: item.Name ?? "",
        Price: item.Price ?? 0,
        Unit: item.Unit ?? "",
        Quantity: item.Quantity ?? 0,
        Amount: item.Amount,
      };
    });
    chargeItems.value = newChargeItems;
  }
};

// 处理治疗点数据函数
const processTreatmentPointData = (data: BaseMoItemData) => {
  if (data.MoItemUseScope === AdviceMoItemUseScope.Community) {
    isShowTreatmentPoint.value = true;
    treatSelectData.value = data.Treats || [];
  } else {
    isShowTreatmentPoint.value = false;
  }
};

const handleGetSelectTrainData = async (defaultId?: string) => {
  const res = await Content_Api.getMoItemActions({
    DtoName: "ActionUnitBasicOutput",
    MoItemId: props.info?.Id || "",
    PageIndex: 1,
    PageSize: 99999,
  });
  if (res.Type === 200) {
    const newData = res.Data.map((item): PageRightTableData => {
      return {
        ...item,
        IsDefault: item.ContentId === defaultId,
      };
    });
    trainingList.value = newData;
  }
};
const handleProcessInfo = (info: BaseMoItemData | null) => {
  if (info) {
    const data: BaseMoItemData = JSON.parse(JSON.stringify(info));

    // 处理场景数据
    const Scene = processSceneData(data);

    // 处理可见性数据
    const Visibility = processVisibility(data);

    // 清理数据属性
    cleanupDataProperties(data);

    // 构建表单项数据
    formItem.value = buildFormItemData(data, Scene, Visibility);

    // 获取已经选择的康复训练
    handleGetSelectTrainData(data.DefaultTrainingItem);

    // 处理收费项目数据
    processChargeItemsData(data);

    // 处理治疗点数据
    processTreatmentPointData(data);
  }
};
interface Props {
  info: BaseMoItemData | null;
}
const props = defineProps<Props>();
watch(
  () => props.info,
  (newVal) => {
    if (newVal) {
      handleProcessInfo(newVal);
    }
  },
  {
    immediate: true,
  }
);
defineExpose({
  handleSubmitData,
});
</script>

<style lang="scss" scoped>
.advice-tabs {
  :deep(.el-tabs__header) {
    position: sticky;
    top: 0;
    z-index: 999;
    background-color: var(--el-bg-color);
  }
}
</style>
