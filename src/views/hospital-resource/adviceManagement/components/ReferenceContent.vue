<template>
  <div class="app-container">
    <BaseTableSearchContainer>
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form ref="queryFormRef" label-position="right" :model="queryParams" :inline="true">
              <el-form-item prop="moItemUseScope" label="使用范围">
                <el-select
                  v-model="queryParams.MoItemUseScope"
                  placeholder="请选择"
                  filterable
                  class="w-80px!"
                  clearable
                  :empty-values="['', null, undefined]"
                  :value-on-clear="() => undefined"
                >
                  <el-option label="居家" :value="AdviceMoItemUseScope.Home" />
                  <el-option label="线下" :value="AdviceMoItemUseScope.Community" />
                  <el-option label="院内" :value="AdviceMoItemUseScope.Hospital" />
                </el-select>
              </el-form-item>
              <el-form-item prop="keywords" label="关键字">
                <el-input
                  v-model="queryParams.keywords"
                  placeholder="名称/编码/拼音码"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          highlight-current-row
          row-key="Id"
          border
          height="100%"
          style="text-align: center; flex: 1"
          @select="handleTableSelect"
          @select-all="handleTableSelect"
        >
          <el-table-column type="selection" width="55" align="center" reserve-selection />
          <el-table-column prop="Name" label="名称" show-overflow-tooltip align="center" />
          <el-table-column prop="PYM" label="拼音码" show-overflow-tooltip align="center" />
          <el-table-column prop="Code" label="编码" show-overflow-tooltip align="center" />
          <el-table-column label="使用范围" show-overflow-tooltip align="center">
            <template #default="scope">
              {{ ["", "居家", "线下", "院内"][scope.row.MoItemUseScope] }}
            </template>
          </el-table-column>
          <el-table-column prop="MoType" label="医嘱类别" width="120" align="center">
            <template #default="scope">
              {{ moItemTypeList.find((item) => item.Value === scope.row.MoType)?.Name || "" }}
            </template>
          </el-table-column>
          <el-table-column label="是否启用" width="100" align="center">
            <template #default="scope">
              {{ scope.row.IsEnable ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            width="150"
            align="center"
            :formatter="tableDateFormat"
          />
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.pageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import Content_Api from "@/api/content";
import { GetReferenceMoItemInputDTO, MoItemPageData } from "@/api/content/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import { AdviceMoItemUseScope } from "@/enums/AdviceEnum";
const { tableLoading, pageData, total, tableRef, selectedTableIds, tableDateFormat } =
  useTableConfig<MoItemPageData>();

const queryParams = ref<GetReferenceMoItemInputDTO>({
  page: 1,
  pageSize: 10,
  keywords: "",
  IsEnable: true,
  CurrentOrganizationId: "",
  MoType: undefined,
  MoItemUseScope: undefined,
});
const moItemTypeList = inject("moItemTypeList") as Ref<MoItemTypeList[]>;
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Content_Api.getReferenceMoItem(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};

const handleQuery = () => {
  queryParams.value.page = 1;
  handleGetTableList();
};

const handleTableSelect = (selection: MoItemPageData[]) => {
  selectedTableIds.value = selection.map((item) => item.Id);
};

const handleSubmitData = (): { OrgId: string; Id: string } | null => {
  if (!selectedTableIds.value.length) {
    ElMessage.error("请选择医嘱");
    return null;
  }
  return {
    OrgId: props.orgId,
    Id: selectedTableIds.value.join(","),
  };
};

const handleClearSelection = () => {
  selectedTableIds.value = [];
  tableRef.value?.clearSelection();
};

interface Props {
  orgId: string;
}
const props = defineProps<Props>();
watch(
  () => props.orgId,
  (newVal) => {
    if (newVal) {
      queryParams.value.CurrentOrganizationId = newVal;
      handleGetTableList();
    }
  },
  {
    immediate: true,
  }
);
defineExpose({
  handleSubmitData,
  handleClearSelection,
  handleGetTableList,
});
</script>

<style lang="scss" scoped></style>
