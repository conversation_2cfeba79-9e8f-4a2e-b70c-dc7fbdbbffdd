<template>
  <div class="container">
    <div v-if="!formData.MFType" class="flex items-center justify-start m-x-10px">
      <span>频次</span>
      <span class="text-red-500 mr-10px">*</span>
      <span class="mr-10px">一天</span>
      <el-input-number
        v-model="formData.Freq"
        :disabled="isOnlyPreview"
        :min="1"
        :step="1"
        step-strictly
      />
      <span class="ml-10px">次</span>
    </div>
    <template v-if="formData.InstrumentParameter?.length">
      <div
        v-for="(item, index) in formData.InstrumentParameter"
        :key="index"
        class="flex items-center justify-start m-x-10px mt-10px"
      >
        <template v-if="!item.NotShow">
          <span class="mr-10px text-right w-60px">{{ item.Name }}</span>
          <span class="text-red-500 mr-10px">*</span>
          <el-select
            v-if="item.Type === 1"
            v-model="item.Value"
            :disabled="isOnlyPreview"
            style="width: 150px"
            @change="(event) => handleItemSelectChange(event, index)"
          >
            <el-option
              v-for="option in item.OptionValue"
              :key="option.Value"
              :label="option.Key"
              :value="option.Value"
            />
          </el-select>
          <el-input-number
            v-if="item.Type === 0"
            v-model="item.Value as number"
            :disabled="isOnlyPreview"
            :step="1"
            step-strictly
            style="width: 150px"
          />
          <span v-if="item.Type === 0" style="color: red">
            范围：{{ item.Min }} - {{ item.Max }}
          </span>
        </template>
      </div>
    </template>
    <div v-if="!formData.MFType" class="flex items-center justify-start m-x-10px mt-10px">
      <span class="mr-10px">运动时间</span>
      <el-input v-model="formData.ActionTime" :disabled="isOnlyPreview" style="flex: 1" />
    </div>
    <div v-if="!formData.MFType" class="flex items-center justify-start m-x-10px mt-10px">
      <span class="mr-10px">运动强度</span>
      <el-input v-model="formData.ActionStrength" :disabled="isOnlyPreview" style="flex: 1" />
    </div>
    <div class="flex items-center justify-start m-x-10px mt-10px">
      <span class="mr-10px">注意事项</span>
      <el-input
        v-model="formData.Notes"
        style="flex: 1"
        :disabled="isOnlyPreview"
        type="textarea"
        show-word-limit
      />
    </div>
    <div class="flex items-center justify-start m-x-10px mt-10px">
      <span class="mr-10px">动作说明</span>
      <el-input
        v-model="formData.ActionInfo"
        style="flex: 1"
        :disabled="isOnlyPreview"
        type="textarea"
        show-word-limit
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import Content_Api from "@/api/content";
import { MoItemAction } from "@/api/content/types";
import { getSkParameter } from "./localSKData";
interface PageMoItemAction extends Omit<MoItemAction, "InstrumentParameter"> {
  InstrumentParameter?: BaseInstrumentParameter[];
}
const isOnlyPreview = inject("isOnlyPreview") as Ref<boolean>;
const formData = reactive<PageMoItemAction>({
  ContentId: "",
  Name: "",
  Enable: false,
  Type: 0,
  Freq: 0,
  Group: "",
  InstrumentId: "",
  InstrumentParameter: [],
  ActionInfo: "",
  Notes: "",
  ActionTime: "",
  ActionStrength: "",
  MFType: 0,
});

const handleSubmit = (): MoItemAction | null => {
  if (!formData.Freq) {
    ElMessage.warning("请输入频次");
    return null;
  }
  if (
    formData.InstrumentParameter?.length &&
    formData.InstrumentParameter.some((item) => item.Value === "")
  ) {
    ElMessage.warning("请将带红点的信息填写完整");
    return null;
  }
  formData.InstrumentParameter &&
    formData.InstrumentParameter.forEach((item) => {
      const ValueLabel = item.OptionValue?.find((v) => v.Value === item.Value);
      item.ValueLabel = ValueLabel?.Key;
      delete item.OptionValue;
      if (item.SignCode === "trainType") {
        item.Unit = item.Value === 1 ? "自动" : "手动";
      }
    });
  return {
    ...formData,
    InstrumentParameter: JSON.stringify(formData.InstrumentParameter),
  };
};

const handleInitPageData = async (data: MoItemAction) => {
  const info: PageMoItemAction = {
    ...data,
    InstrumentParameter: [],
  };
  if (data.MFType) {
    let pageInstrumentParameter: BaseInstrumentParameter[] = data.InstrumentParameter
      ? JSON.parse(data.InstrumentParameter)
      : [];
    if (!pageInstrumentParameter.length) {
      info.InstrumentParameter = returnBackData(data.MFType);
    } else {
      pageInstrumentParameter.forEach((s) => {
        if (!s.SignCode) s.SignCode = s.Key;
      });
      const baseInstrumentParameter = returnBackData(data.MFType);
      baseInstrumentParameter.forEach((v: BaseInstrumentParameter) => {
        pageInstrumentParameter.forEach((e: BaseInstrumentParameter) => {
          if (v.SignCode === e.SignCode) {
            v.Value = e.Value;
          }
        });
      });
      info.InstrumentParameter = baseInstrumentParameter;
    }
  } else {
    if (data.InstrumentId) {
      const res = await Content_Api.getInstrumentByIds([data.InstrumentId]);
      if (res.Type === 200) {
        if (res.Data[0].Parameters) {
          if (!data.InstrumentParameter) {
            info.InstrumentParameter = JSON.parse(res.Data[0].Parameters);
          } else {
            const beforeInstrumentParameter = JSON.parse(data.InstrumentParameter);
            beforeInstrumentParameter.forEach((s: BaseInstrumentParameter) => {
              if (!s.SignCode) s.SignCode = s.Key;
            });
            const nowInstrumentParameter = JSON.parse(res.Data[0].Parameters);
            nowInstrumentParameter.forEach((v: BaseInstrumentParameter) => {
              beforeInstrumentParameter.forEach((e: BaseInstrumentParameter) => {
                if (v.SignCode === e.SignCode) {
                  v.Value = e.Value;
                }
              });
            });
            info.InstrumentParameter = nowInstrumentParameter;
          }
        }
      }
    } else {
      info.InstrumentParameter = data.InstrumentParameter
        ? JSON.parse(data.InstrumentParameter)
        : [];
    }
  }
  return info;
};
const returnBackData = (MFType: number): BaseInstrumentParameter[] => {
  let InstrumentParameter: BaseInstrumentParameter[] = [];
  if (MFType === 3) {
    const SkParameter = getSkParameter(MFType);
    // 起到廓清
    InstrumentParameter = [
      {
        Name: "治疗时间",
        Type: 1,
        Value: 1,
        Unit: "分钟",
        SignCode: "times",
        OptionValue: SkParameter["times"],
      },
      {
        Name: "治疗频次",
        Type: 1,
        Value: 3,
        Unit: "次",
        SignCode: "count",
        OptionValue: SkParameter["count"],
      },
      {
        Name: "训练模式",
        Type: 1,
        Value: 1,
        OptionValue: SkParameter["trainType"],
        Unit: "",
        SignCode: "trainType",
      },
      {
        Name: "阻力挡位",
        Type: 1,
        Value: 1,
        OptionValue: SkParameter["level"],
        Unit: "档",
        SignCode: "level",
      },
      {
        NotShow: true,
        Name: "振动频率",
        Type: 1,
        Value: 10,
        Unit: "HZ",
        SignCode: "f",
        OptionValue: SkParameter["f"],
      },
    ];
  } else {
    const SkParameter = getSkParameter(2);
    // 吸气和呼气
    InstrumentParameter = [
      {
        Name: "训练次数",
        Type: 1,
        Value: 30,
        Unit: "次",
        SignCode: "times",
        OptionValue: SkParameter["times"],
      },
      {
        Name: "训练模式",
        Type: 1,
        Value: 1,
        OptionValue: SkParameter["trainType"],
        Unit: "",
        SignCode: "trainType",
      },
      {
        NotShow: true,
        Name: "阻抗",
        Type: 1,
        Value: 3,
        Unit: "cmH2O",
        SignCode: "pload",
        OptionValue: SkParameter["pload"],
      },
      {
        Name: "难度级别",
        Type: 1,
        Value: 1,
        OptionValue: SkParameter["difficult"],
        Unit: "星",
        SignCode: "difficult",
      },
      {
        Name: "训练频次",
        Type: 1,
        Value: 1,
        OptionValue: SkParameter["count"],
        Unit: "次",
        SignCode: "count",
      },
    ];
  }
  return InstrumentParameter;
};
const handleMFType3Change = (event: number, index: number) => {
  if (index === 2) {
    formData.InstrumentParameter![4].NotShow = event !== 2;
  } else if (index === 1) {
    formData.Freq = event;
  }
};

const handleMFType2Change = (event: number, index: number) => {
  if (index === 4) {
    formData.Freq = event;
  } else if (index === 1) {
    const isManual = event === 2;
    formData.InstrumentParameter![3].NotShow = isManual;
    formData.InstrumentParameter![2].NotShow = !isManual;
  }
};

const handleItemSelectChange = (event: number, index: number) => {
  if (formData.MFType === 3) {
    handleMFType3Change(event, index);
  } else {
    handleMFType2Change(event, index);
  }
};

interface Props {
  updateActionData: MoItemAction | null;
}
const props = defineProps<Props>();
watch(
  () => props.updateActionData,
  async (newVal) => {
    if (newVal) {
      const info = await handleInitPageData(newVal);
      Object.assign(formData, info);
    }
  },
  {
    immediate: true,
  }
);
defineExpose({
  handleSubmit,
});
</script>
<style lang="scss" scoped>
.container {
  padding: 20px;
  height: 500px;
  overflow-y: auto;
  width: 100%;
}
</style>
