<template>
  <div class="container">
    <div class="flex items-center justify-start">
      <div class="flex items-center justify-start">
        <span>疾病</span>
        <el-select
          v-model="queryData.Diseases"
          multiple
          :disabled="isOnlyPreview"
          :multiple-limit="1"
          placeholder="请选择疾病"
          style="width: 180px; margin: 0 10px"
          clearable
        >
          <el-option
            v-for="item in diseaseList"
            :key="item.Id"
            :label="item.Key"
            :value="item.Id!"
          />
        </el-select>
      </div>
      <div class="flex items-center justify-start">
        <span>科室</span>
        <el-select
          v-model="queryData.DeptId"
          :disabled="isOnlyPreview"
          placeholder="请选择科室"
          style="width: 180px; margin: 0 10px"
          clearable
        >
          <el-option v-for="item in deptList" :key="item.Id" :label="item.Name" :value="item.Id!" />
        </el-select>
      </div>
      <el-button type="primary" :disabled="isOnlyPreview" @click="handleResetMissionaryListData">
        搜索
      </el-button>
    </div>
    <div class="mt-10px m-x-auto">
      <el-transfer
        v-model="selectedMissionaryIds"
        filterable
        :disabled="isOnlyPreview"
        :filter-method="filterMethod"
        filter-placeholder="请输入名称"
        :data="missionaryList"
        :props="{
          key: 'ContentId',
          label: 'Title',
        }"
        :titles="['待选', '已选']"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { PageQueryContentInputDTO } from "@/api/content/types";
import { RxTemplateMoItemDetailInputDTO } from "@/api/consult/types";
import Content_Api from "@/api/content";
import { TransferDataItem } from "element-plus";
const selectedMissionaryIds = ref<string[]>([]);
const missionaryList = ref<BaseRecoveryMission[]>([]);
const allMissionaryList = ref<BaseRecoveryMission[]>([]);
const deptList = inject("deptList") as Ref<BaseDepartment[]>;
const diseaseList = inject("diseaseList") as Ref<ReadDict[]>;
const isOnlyPreview = inject("isOnlyPreview") as Ref<boolean>;
const queryTitle = ref<string>("");
const queryData = ref<PageQueryContentInputDTO>({
  Enable: true,
  PageIndex: 1,
  PageSize: 9999,
  RecoveryMissionType: "",
  KeyWord: "",
  DeptId: "",
  Diseases: [],
});
const formData = reactive<RxTemplateMoItemDetailInputDTO>({
  MoDay: 1,
  FreqDay: 1,
  Freq: 1,
  TotalCount: 1,
  Consumables: [],
  MoItemId: "",
  MoName: "",
  MoRemark: "",
  Price: 0,
  IsSpecialFreq: false,
  MoItemMethod: 0,
  MoItemUseScope: 0,
  LogisticsDay: 0,
});
const handleFetchRecoveryMissionTypes = async () => {
  const res = await Content_Api.getAllRecoveryMissionTypes({
    organizationId: "",
    isShowOperation: false,
    isShowDisease: true,
    isShowRegime: false,
  });
  if (res.Type === 200) {
    queryData.value.RecoveryMissionType = res.Data?.[0].Children?.[0].Id;
    handleFetchMissionaryListData();
  }
};
const handleFetchMissionaryListData = async () => {
  queryData.value.OrganizationId = props.orgId;
  const res = await Content_Api.pageQueryContent(queryData.value);
  if (res.Type === 200) {
    missionaryList.value = res.Data.Data;
    allMissionaryList.value = res.Data.Data;
  } else {
    missionaryList.value = [];
    allMissionaryList.value = [];
  }
};
const handleResetMissionaryListData = () => {
  missionaryList.value = allMissionaryList.value;
  const { Diseases, DeptId } = queryData.value;

  missionaryList.value = missionaryList.value.filter((item) => {
    let matchDept = true;
    let matchDisease = true;
    let matchTitle = true;
    if (DeptId) {
      matchDept = (item.Depts || []).includes(DeptId);
    }

    if (Diseases && Diseases.length > 0) {
      matchDisease = (item.Diseases || []).includes(Diseases[0]);
    }
    if (queryTitle.value && item.Title) {
      matchTitle = item.Title.includes(queryTitle.value);
    }

    return matchDept && matchDisease && matchTitle;
  });
};
const filterMethod = (query: string, item: TransferDataItem) => {
  queryTitle.value = query;
  return item.Title.includes(query);
};
const handleSubmit = (): RxTemplateMoItemDetailInputDTO => {
  return {
    MoItemId: formData.MoItemId,
    MoName: formData.MoName,
    MoRemark: formData.MoRemark,
    MoDay: formData.MoDay || 1,
    Freq: formData.Freq || 1,
    Part: formData.Part || 1,
    TotalCount: formData.TotalCount,
    FreqDay: formData.FreqDay || 1,
    IsSpecialFreq: formData.IsSpecialFreq,
    MoItemMethod: formData.MoItemMethod,
    MoItemUseScope: formData.MoItemUseScope,
    LogisticsDay: formData.LogisticsDay,
    AcupointInputDtos: [],
    ActionInputDtos: [],
    ScaleInputDtos: [],
    Price: formData.Price,
    ChargeMode: formData.ChargeMode,
    ChargeItem: formData.ChargeItem,
    RecoveryMissionRelations: selectedMissionaryIds.value.map((id) => ({
      Id: "",
      RecoveryMissionId: id,
    })),
    PackId: formData.PackId,
    MinDay: formData.MinDay,
    MaxDay: formData.MaxDay,
  };
};

interface Props {
  moItemId: string;
  info: RxTemplateMoItemDetailInputDTO | null;
  orgId: string;
}
const props = defineProps<Props>();
watch(
  () => props.info,
  (newVal) => {
    if (newVal) {
      // 使用 Object.assign 更新属性，保持响应式
      if (newVal.RecoveryMissionRelations && newVal.RecoveryMissionRelations.length) {
        selectedMissionaryIds.value = newVal.RecoveryMissionRelations.map(
          (item) => item.RecoveryMissionId
        );
      }
      Object.assign(formData, newVal);
    }
  },
  {
    immediate: true,
  }
);
defineExpose({
  handleSubmit,
});
onMounted(() => {
  handleFetchRecoveryMissionTypes();
});
</script>

<style lang="scss" scoped>
.container {
  height: 400px;
  overflow-y: auto;
  width: 100%;
  padding: 20px;
}
:deep(.el-transfer-panel) {
  width: calc(50% - 100px);
}
</style>
